import "@/app/globals.css";
import { libreBaskerville, figTree, slackey, inter } from '@/app/style_vars';

export const metadata = {
  title: "What-if - AI-Powered Alternate timeline Storytelling",
  description: "Transform Original Stories into interactive alternate timeline stories with AI. Explore 'what-if' scenarios through engaging narratives powered by Google's Gemini AI.",
  keywords: "AI storytelling, interactive narratives, Gemini AI, story analysis, alternate timelines",
  openGraph: {
    title: "What-if - AI-Powered Alternate timeline Storytelling",
    description: "Transform Original Stories into interactive alternate timeline stories with AI.",
    type: "website"
  }
};

export default function HomeLayout({ children }) {
  return (
    <html lang="en">
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </head>
      <body  className={`${libreBaskerville.variable} ${slackey.variable} ${figTree.variable} ${inter.variable} antialiased`}>
        {children}
      </body>
    </html>
  );
} 