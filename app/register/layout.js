import "../globals.css";
import { libreBaskerville, figTree, slackey, inter } from '../style_vars';

export const metadata = {
  title: "Coming Soon - The Money Tales",
  description: "We are crafting something cool. Join our waiting list now.",
};

export default async function RegisterLayout({ children }) {
  // Session management removed - authentication disabled
  return (
    <div className={`${libreBaskerville.variable} ${slackey.variable} ${figTree.variable} ${inter.variable}`}>
      {children}
    </div>
  );
}
