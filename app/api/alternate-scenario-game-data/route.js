import { NextResponse } from 'next/server';
import { GoogleGenAI } from '@google/genai';

// Image generation function (extracted from generate-image API)
async function generateImageWithFreepik(prompt) {
  try {
    // Get Freepik API key
    const apiKey = process.env.FREEPIK_API_KEY;
    if (!apiKey) {
      throw new Error('Freepik API key not configured');
    }

    // Prepare the request payload for Freepik API
    const freepikPayload = {
      prompt: prompt,
      negative_prompt: "low quality, blurry, distorted, ugly, bad anatomy, watermark, text, signature, unclear faces, messy composition, poor lighting",
      guidance_scale: 8.0,
      num_images: 1,
      image: {
        size: "landscape_16_9"
      },
      styling: {
        style: "digital-art",
        effects: {
          color: "vibrant",
          lightning: "dramatic"
        }
      },
      filter_nsfw: true
    };

    console.log('Generating image with Freepik API...');
    console.log('Prompt:', prompt);

    // Call Freepik API
    let freepikResponse = await fetch('https://api.freepik.com/v1/ai/text-to-image', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-freepik-api-key': apiKey,
      },
      body: JSON.stringify(freepikPayload),
    });

    let freepikData = await freepikResponse.json();

    // If styling parameters failed, try again with minimal payload
    if (!freepikResponse.ok && freepikData.message?.includes('styling')) {
      console.log('Styling parameters failed, trying with minimal payload...');

      const minimalPayload = {
        prompt: prompt,
        num_images: 1,
        image: {
          size: "landscape_16_9"
        },
        filter_nsfw: true
      };

      freepikResponse = await fetch('https://api.freepik.com/v1/ai/text-to-image', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-freepik-api-key': apiKey,
        },
        body: JSON.stringify(minimalPayload),
      });

      freepikData = await freepikResponse.json();
    }

    if (!freepikResponse.ok) {
      console.error('Freepik API error:', freepikData);
      throw new Error(freepikData.message || `Freepik API error: ${freepikResponse.status}`);
    }

    // Check if we got valid image data
    if (!freepikData.data || !freepikData.data[0] || !freepikData.data[0].base64) {
      console.error('Invalid response from Freepik API:', freepikData);
      throw new Error('Invalid response from Freepik API');
    }

    const imageData = freepikData.data[0];
    const metadata = freepikData.meta;

    console.log('Image generated successfully');

    return {
      success: true,
      data: {
        base64: imageData.base64,
        has_nsfw: imageData.has_nsfw,
        metadata: {
          width: metadata.image.width,
          height: metadata.image.height,
          size: metadata.image.size,
          prompt: metadata.prompt,
          seed: metadata.seed,
          guidance_scale: metadata.guidance_scale
        }
      }
    };
  } catch (error) {
    console.error('Error generating image with Freepik:', error);
    return {
      success: false,
      error: error.message || 'Unknown error occurred'
    };
  }
}

// Initialize the Gemini API client
const apiKey = process.env.GEMINI_API_KEY;
const genAI = new GoogleGenAI({ apiKey });

// Enhanced helper function to fix common JSON issues
function fixJsonString(jsonStr) {
  let fixed = jsonStr;

  // Remove trailing commas before closing brackets/braces
  fixed = fixed.replace(/,(\s*[}\]])/g, '$1');

  // Fix newlines in strings
  fixed = fixed.replace(/"\s*\n\s*"/g, '",\n"');

  // Fix missing commas between objects
  fixed = fixed.replace(/}(\s*)"([^"]+)":/g, '},\n"$2":');
  fixed = fixed.replace(/}(\s*)"/g, '},\n"');
  fixed = fixed.replace(/"(\s*)"([^"]+)":/g, '",\n"$2":');

  // Fix unescaped quotes in string values
  fixed = fixed.replace(/:\s*"([^"]*)"([^",}\]]*)"([^",}\]]*)"(\s*[,}\]])/g, (_, p1, p2, p3, p4) => {
    const content = (p1 + '"' + p2 + '"' + p3).replace(/"/g, '\\"');
    return `: "${content}"${p4}`;
  });

  // Fix newlines within string values
  fixed = fixed.replace(/:\s*"([^"]*)\n([^"]*)"(\s*[,}\]])/g, ': "$1\\n$2"$3');

  // Fix single quotes to double quotes
  fixed = fixed.replace(/:\s*'([^']*)'/g, ': "$1"');

  // Fix unquoted property names
  fixed = fixed.replace(/([{,]\s*)([a-zA-Z_][a-zA-Z0-9_]*)\s*:/g, '$1"$2":');

  // Fix quoted boolean/null values
  fixed = fixed.replace(/:\s*"(true|false|null)"/g, ': $1');

  // Fix multiple consecutive commas
  fixed = fixed.replace(/,+/g, ',');

  // Fix missing commas between array elements
  fixed = fixed.replace(/"\s*\n\s*"/g, '",\n"');
  fixed = fixed.replace(/}(\s*){/g, '},\n{');

  // Fix incomplete strings at the end
  fixed = fixed.replace(/:\s*"([^"]*?)$/, ': "$1"');

  return fixed;
}

// Enhanced helper function to safely parse JSON from AI responses
function safeJsonParse(text, context = '') {
  console.log(`Attempting to parse JSON for ${context}`);
  console.log('Raw text:', text.substring(0, 500) + '...');

  // Strategy 1: Try basic cleaning and parsing
  try {
    let cleanedText = text.replace(/```json\n?|\n?```/g, '').trim();
    cleanedText = cleanedText.replace(/[\x00-\x1F\x7F-\x9F]/g, ' ');
    cleanedText = cleanedText.replace(/[\u2018\u2019]/g, "'");
    cleanedText = cleanedText.replace(/[\u201C\u201D]/g, '"');
    cleanedText = cleanedText.replace(/\u2026/g, '...');

    const jsonObjectMatch = cleanedText.match(/(\{[\s\S]*\})/);
    if (jsonObjectMatch && jsonObjectMatch[1]) {
      cleanedText = jsonObjectMatch[1];
    }

    cleanedText = fixJsonString(cleanedText);
    console.log(`Cleaned text for ${context}:`, cleanedText.substring(0, 300) + '...');
    return JSON.parse(cleanedText);
  } catch (firstError) {
    console.log(`First parsing attempt failed for ${context}:`, firstError.message);

    // Strategy 2: Try more aggressive cleaning
    try {
      let aggressiveClean = text.replace(/```json\n?|\n?```/g, '').trim();

      // Remove any text before the first {
      const firstBrace = aggressiveClean.indexOf('{');
      if (firstBrace > 0) {
        aggressiveClean = aggressiveClean.substring(firstBrace);
      }

      // Remove any text after the last }
      const lastBrace = aggressiveClean.lastIndexOf('}');
      if (lastBrace > 0) {
        aggressiveClean = aggressiveClean.substring(0, lastBrace + 1);
      }

      // Fix common issues
      aggressiveClean = aggressiveClean.replace(/[\x00-\x1F\x7F-\x9F]/g, ' ');
      aggressiveClean = aggressiveClean.replace(/[\u2018\u2019]/g, "'");
      aggressiveClean = aggressiveClean.replace(/[\u201C\u201D]/g, '"');
      aggressiveClean = aggressiveClean.replace(/\u2026/g, '...');

      // Fix unescaped quotes in text content
      aggressiveClean = aggressiveClean.replace(/"text":\s*"([^"]*)"([^"]*)"([^"]*)"(\s*[,}])/g, (_, p1, p2, p3, p4) => {
        const content = (p1 + '\\"' + p2 + '\\"' + p3);
        return `"text": "${content}"${p4}`;
      });

      aggressiveClean = fixJsonString(aggressiveClean);
      console.log(`Aggressively cleaned text for ${context}:`, aggressiveClean.substring(0, 300) + '...');
      return JSON.parse(aggressiveClean);
    } catch (secondError) {
      console.log(`Second parsing attempt failed for ${context}:`, secondError.message);

      // Strategy 3: Try to extract and reconstruct the JSON manually
      try {
        const titleMatch = text.match(/"title":\s*"([^"]*(?:\\"[^"]*)*)"/);
        const textMatch = text.match(/"text":\s*"([^"]*(?:\\"[^"]*)*(?:[^"\\]|\\.)*)"/);
        const idMatch = text.match(/"id":\s*"([^"]*)"/);

        if (context.includes('Root scenario')) {
          // Try to reconstruct root scenario
          const choicesMatch = text.match(/"choices":\s*\[([\s\S]*?)\]/);
          let choices = [];

          if (choicesMatch) {
            const choiceText = choicesMatch[1];
            const choiceMatches = choiceText.match(/\{[^}]*\}/g);
            if (choiceMatches) {
              choices = choiceMatches.map((choice, index) => {
                const choiceIdMatch = choice.match(/"id":\s*"([^"]*)"/);
                const choiceTextMatch = choice.match(/"text":\s*"([^"]*(?:\\"[^"]*)*)"/);
                return {
                  id: choiceIdMatch ? choiceIdMatch[1] : `choice${index + 1}`,
                  text: choiceTextMatch ? choiceTextMatch[1].replace(/\\"/g, '"') : `Choice ${index + 1}`
                };
              });
            }
          }

          if (!choices.length) {
            choices = [
              { id: "choice1", text: "Continue the story" },
              { id: "choice2", text: "Take a different path" },
              { id: "choice3", text: "Explore alternatives" },
              { id: "choice4", text: "Try something new" }
            ];
          }

          return {
            rootScenario: {
              id: idMatch ? idMatch[1] : "root",
              title: titleMatch ? titleMatch[1].replace(/\\"/g, '"') : "Alternate Timeline",
              text: textMatch ? textMatch[1].replace(/\\"/g, '"') : "The story continues with new possibilities...",
              choices: choices
            }
          };
        } else {
          // Try to reconstruct level 2/3 scenario
          const isEnding = context.includes('Level 3') || text.includes('"isEnding"');
          let choices = [];

          if (!isEnding) {
            const choicesMatch = text.match(/"choices":\s*\[([\s\S]*?)\]/);
            if (choicesMatch) {
              const choiceText = choicesMatch[1];
              const choiceMatches = choiceText.match(/\{[^}]*\}/g);
              if (choiceMatches) {
                choices = choiceMatches.map((choice, index) => {
                  const choiceIdMatch = choice.match(/"id":\s*"([^"]*)"/);
                  const choiceTextMatch = choice.match(/"text":\s*"([^"]*(?:\\"[^"]*)*)"/);
                  return {
                    id: choiceIdMatch ? choiceIdMatch[1] : `choice_${index + 1}`,
                    text: choiceTextMatch ? choiceTextMatch[1].replace(/\\"/g, '"') : `Choice ${index + 1}`
                  };
                });
              }
            }
          }

          const scenario = {
            id: idMatch ? idMatch[1] : "scenario",
            title: titleMatch ? titleMatch[1].replace(/\\"/g, '"') : "Story Continues",
            text: textMatch ? textMatch[1].replace(/\\"/g, '"') : "The alternate timeline unfolds with new developments..."
          };

          if (choices.length > 0) {
            scenario.choices = choices;
          }

          if (isEnding) {
            scenario.isEnding = true;
          }

          return { scenario };
        }
      } catch (thirdError) {
        console.error(`All parsing strategies failed for ${context}:`, thirdError.message);
        console.error('Original text:', text);
        throw new Error(`Failed to parse JSON for ${context}: ${firstError.message}`);
      }
    }
  }
}

// Transform story tree data into Godot-compatible quiz format
function transformStoryTreeToQuizFormat(storyTree) {
  const questions = [];
  
  // Add root scenario as first question
  if (storyTree.root) {
    const rootQuestion = {
      question: storyTree.root.text,
      title: storyTree.root.title,
      options: storyTree.root.choices ? storyTree.root.choices.map(choice => choice.text) : [],
      correct_answer: -1, // No correct answer for story choices
      choice_ids: storyTree.root.choices ? storyTree.root.choices.map(choice => choice.id) : [],
      level: 0,
      node_id: storyTree.root.id
    };

    // Add image data if available
    if (storyTree.root.image) {
      rootQuestion.image = storyTree.root.image;
    }

    // Add image prompt if available
    if (storyTree.root.imagePrompt) {
      rootQuestion.imagePrompt = storyTree.root.imagePrompt;
    }

    questions.push(rootQuestion);
  }

  // Add level 2 scenarios
  if (storyTree.level2) {
    Object.values(storyTree.level2).forEach(scenario => {
      questions.push({
        question: scenario.text,
        title: scenario.title,
        options: scenario.choices ? scenario.choices.map(choice => choice.text) : [],
        correct_answer: -1,
        choice_ids: scenario.choices ? scenario.choices.map(choice => choice.id) : [],
        level: 1,
        node_id: scenario.id,
        is_ending: scenario.isEnding || false
      });
    });
  }

  // Add level 3 scenarios (endings)
  if (storyTree.level3) {
    Object.values(storyTree.level3).forEach(scenario => {
      questions.push({
        question: scenario.text,
        title: scenario.title,
        options: [], // No choices for endings
        correct_answer: -1,
        choice_ids: [],
        level: 2,
        node_id: scenario.id,
        is_ending: true
      });
    });
  }

  return {
    book_info: storyTree.bookInfo || {},
    story_tree: storyTree,
    questions: questions
  };
}

export async function POST(request) {
  try {
    const { bookTitle, author, changeLocation, whatIfPrompt } = await request.json();

    if (!bookTitle || !author || !changeLocation || !whatIfPrompt) {
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      );
    }

    if (!apiKey) {
      return NextResponse.json(
        { error: 'API key not configured' },
        { status: 500 }
      );
    }

    const model = 'gemini-2.0-flash-001';

    // Generate the root scenario (simplified version for game)
    const rootPrompt = `
You are a creative fiction writer specializing in alternate timeline stories. Based on the book "${bookTitle}" by ${author}, create an engaging alternate scenario starting from ${changeLocation}.

User's "What If" scenario: ${whatIfPrompt}

IMPORTANT: Respond with ONLY valid JSON. Do not include any text before or after the JSON. Avoid quotes within text content - use simple punctuation instead.

Generate a JSON response with the following structure:
{
  "rootScenario": {
    "id": "root",
    "title": "Brief compelling title for this scenario",
    "text": "A complete engaging narrative of 200-300 words describing the alternate scenario. Make it vivid and immersive. Avoid using quotes or apostrophes in the text.",
    "imagePrompt": "A detailed Freepik image generation prompt describing a key scene from this scenario. Include context that this is an illustration from the book '${bookTitle}' by ${author}. Be very descriptive about the setting, characters, mood, and visual elements.",
    "choices": [
      {
        "id": "choice1",
        "text": "First choice option (20-30 words)"
      },
      {
        "id": "choice2",
        "text": "Second choice option (20-30 words)"
      },
      {
        "id": "choice3",
        "text": "Third choice option (20-30 words)"
      },
      {
        "id": "choice4",
        "text": "Fourth choice option (20-30 words)"
      }
    ]
  }
}

Make sure each choice leads to meaningfully different story directions. The scenario should feel authentic to the original book's tone and style. Remember: ONLY return valid JSON, no other text.
`;

    const rootResult = await genAI.models.generateContent({
      model,
      contents: [
        { role: 'user', parts: [{ text: rootPrompt }] }
      ],
      config: {
        temperature: 0.7,
        topP: 0.9,
        topK: 40,
        maxOutputTokens: 1024,
      },
    });

    let rootText = '';
    if (rootResult.candidates && rootResult.candidates.length > 0) {
      const candidate = rootResult.candidates[0];
      if (candidate.content && candidate.content.parts && candidate.content.parts.length > 0) {
        rootText = candidate.content.parts[0].text || '';
      }
    }

    let rootScenario;
    try {
      rootScenario = safeJsonParse(rootText, 'Root scenario');
    } catch (parseError) {
      console.error('Error parsing root scenario JSON:', parseError);
      return NextResponse.json(
        { error: 'Failed to parse root scenario response' },
        { status: 500 }
      );
    }

    // Generate Level 2 scenarios for each choice
    const level2Scenarios = {};

    for (const choice of rootScenario.rootScenario.choices) {
      const level2Prompt = `
Continue the alternate timeline story from "${bookTitle}" by ${author}.

Previous scenario: ${rootScenario.rootScenario.text}

The reader chose: "${choice.text}"

IMPORTANT: Respond with ONLY valid JSON. Do not include any text before or after the JSON. Avoid quotes within text content - use simple punctuation instead.

Generate a JSON response with this structure:
{
  "scenario": {
    "id": "${choice.id}",
    "title": "Brief compelling title for this continuation",
    "text": "A complete narrative of 200-300 words continuing from the chosen path. Show the consequences of the choice and develop the story further. Avoid using quotes or apostrophes in the text.",
    "choices": [
      {
        "id": "${choice.id}_1",
        "text": "First choice option (20-30 words)"
      },
      {
        "id": "${choice.id}_2",
        "text": "Second choice option (20-30 words)"
      },
      {
        "id": "${choice.id}_3",
        "text": "Third choice option (20-30 words)"
      },
      {
        "id": "${choice.id}_4",
        "text": "Fourth choice option (20-30 words)"
      }
    ]
  }
}

Maintain consistency with the original book's tone and the established alternate timeline. Remember: ONLY return valid JSON, no other text.
`;

      try {
        const level2Result = await genAI.models.generateContent({
          model,
          contents: [
            { role: 'user', parts: [{ text: level2Prompt }] }
          ],
          config: {
            temperature: 0.7,
            topP: 0.9,
            topK: 40,
            maxOutputTokens: 1024,
          },
        });

        let level2Text = '';
        if (level2Result.candidates && level2Result.candidates.length > 0) {
          const candidate = level2Result.candidates[0];
          if (candidate.content && candidate.content.parts && candidate.content.parts.length > 0) {
            level2Text = candidate.content.parts[0].text || '';
          }
        }

        const level2Data = safeJsonParse(level2Text, `Level 2 scenario for ${choice.id}`);
        level2Scenarios[choice.id] = level2Data.scenario;
      } catch (error) {
        console.error(`Error generating level 2 scenario for ${choice.id}:`, error);
        // Create a fallback scenario
        level2Scenarios[choice.id] = {
          id: choice.id,
          title: "Story Continues...",
          text: "The story continues from your choice. More adventures await as the alternate timeline unfolds with unexpected consequences and new possibilities.",
          choices: [
            { id: `${choice.id}_1`, text: "Continue the adventure" },
            { id: `${choice.id}_2`, text: "Take a different approach" },
            { id: `${choice.id}_3`, text: "Seek help from allies" },
            { id: `${choice.id}_4`, text: "Face the challenge alone" }
          ]
        };
      }
    }

    // Generate Level 3 scenarios (endings) for each level 2 choice
    const level3Scenarios = {};

    for (const [, parentScenario] of Object.entries(level2Scenarios)) {
      if (parentScenario.choices) {
        for (const level2Choice of parentScenario.choices) {
          const level3Prompt = `
Continue the alternate timeline story from "${bookTitle}" by ${author}.

Previous scenario: ${parentScenario.text}

The reader chose: "${level2Choice.text}"

IMPORTANT: Respond with ONLY valid JSON. Do not include any text before or after the JSON. Avoid quotes within text content - use simple punctuation instead.

Generate a JSON response with this structure:
{
  "scenario": {
    "id": "${level2Choice.id}",
    "title": "Brief compelling title for this final chapter",
    "text": "A complete narrative of 250-350 words bringing this story branch to a satisfying conclusion. Show the ultimate consequences of all the choices made and provide closure to this alternate timeline. Avoid using quotes or apostrophes in the text.",
    "isEnding": true
  }
}

This should be a concluding chapter that wraps up this particular story branch. Maintain consistency with the original book's tone and the established alternate timeline. Remember: ONLY return valid JSON, no other text.
`;

          try {
            const level3Result = await genAI.models.generateContent({
              model,
              contents: [
                { role: 'user', parts: [{ text: level3Prompt }] }
              ],
              config: {
                temperature: 0.7,
                topP: 0.9,
                topK: 40,
                maxOutputTokens: 1024,
              },
            });

            let level3Text = '';
            if (level3Result.candidates && level3Result.candidates.length > 0) {
              const candidate = level3Result.candidates[0];
              if (candidate.content && candidate.content.parts && candidate.content.parts.length > 0) {
                level3Text = candidate.content.parts[0].text || '';
              }
            }

            const level3Data = safeJsonParse(level3Text, `Level 3 scenario for ${level2Choice.id}`);
            level3Scenarios[level2Choice.id] = level3Data.scenario;
          } catch (error) {
            console.error(`Error generating level 3 scenario for ${level2Choice.id}:`, error);
            // Create a fallback ending scenario
            level3Scenarios[level2Choice.id] = {
              id: level2Choice.id,
              title: "The Story Concludes",
              text: "The alternate timeline reaches its conclusion. The choices made have led to this moment, where the consequences of change ripple through the narrative, creating a new ending to a familiar tale.",
              isEnding: true
            };
          }
        }
      }
    }

    const storyTree = {
      success: true,
      root: rootScenario.rootScenario,
      level2: level2Scenarios,
      level3: level3Scenarios,
      bookInfo: {
        title: bookTitle,
        author: author,
        changeLocation: changeLocation,
        whatIfPrompt: whatIfPrompt
      }
    };

    // Generate image for the root scenario if imagePrompt exists
    let rootImage = null;
    if (storyTree.root.imagePrompt) {
      try {
        console.log('Generating image for root scenario...');
        const imageResult = await generateImageWithFreepik(storyTree.root.imagePrompt);

        if (imageResult.success) {
          rootImage = imageResult.data;
          console.log('Root scenario image generated successfully');
        } else {
          console.error('Failed to generate root image:', imageResult.error);
        }
      } catch (imageError) {
        console.error('Error generating root scenario image:', imageError);
      }
    }

    // Add image to story tree
    if (rootImage) {
      storyTree.root.image = rootImage;
    }

    // Transform to Godot-compatible format
    const gameData = transformStoryTreeToQuizFormat(storyTree);

    return NextResponse.json(gameData);

  } catch (error) {
    console.error('Error generating alternate scenario game data:', error);
    return NextResponse.json(
      { error: 'Failed to generate game data' },
      { status: 500 }
    );
  }
}
