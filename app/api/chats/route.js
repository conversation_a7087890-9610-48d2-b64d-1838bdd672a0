import { NextResponse } from 'next/server';
import { 
  collection, 
  addDoc, 
  getDocs, 
  getDoc, 
  updateDoc, 
  deleteDoc, 
  doc, 
  query, 
  where, 
  serverTimestamp 
} from 'firebase/firestore';
import { db } from '@/lib/firebase';

// GET all chats for a user
export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json(
        { error: 'Missing userId parameter' },
        { status: 400 }
      );
    }

    const chatsRef = collection(db, 'chats');
    const q = query(chatsRef, where('userId', '==', userId));
    const querySnapshot = await getDocs(q);
    
    const chats = [];
    querySnapshot.forEach((doc) => {
      chats.push({
        id: doc.id,
        ...doc.data()
      });
    });

    return NextResponse.json({ chats });
  } catch (error) {
    console.error('Error fetching chats:', error);
    return NextResponse.json(
      { error: 'Failed to fetch chats' },
      { status: 500 }
    );
  }
}

// POST create a new chat
export async function POST(request) {
  try {
    const { userId, title, bookInfo } = await request.json();

    if (!userId) {
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      );
    }

    const chatData = {
      userId,
      title: title || 'New Chat',
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      bookInfo: bookInfo || {}
    };

    const docRef = await addDoc(collection(db, 'chats'), chatData);
    
    return NextResponse.json({
      success: true,
      id: docRef.id,
      ...chatData
    });
  } catch (error) {
    console.error('Error creating chat:', error);
    return NextResponse.json(
      { error: 'Failed to create chat' },
      { status: 500 }
    );
  }
}