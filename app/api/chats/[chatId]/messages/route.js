import { NextResponse } from 'next/server';
import { 
  collection, 
  addDoc, 
  getDocs, 
  query, 
  orderBy, 
  serverTimestamp 
} from 'firebase/firestore';
import { db } from '@/lib/firebase';

// GET all messages for a chat
export async function GET(request, { params }) {
  try {
    const { chatId } = params;
    
    if (!chatId) {
      return NextResponse.json(
        { error: 'Missing chat ID' },
        { status: 400 }
      );
    }

    const messagesRef = collection(db, 'chats', chatId, 'messages');
    const q = query(messagesRef, orderBy('timestamp', 'asc'));
    const querySnapshot = await getDocs(q);
    
    const messages = [];
    querySnapshot.forEach((doc) => {
      messages.push({
        id: doc.id,
        ...doc.data()
      });
    });

    return NextResponse.json({ messages });
  } catch (error) {
    console.error('Error fetching messages:', error);
    return NextResponse.json(
      { error: 'Failed to fetch messages' },
      { status: 500 }
    );
  }
}

// POST create a new message
export async function POST(request, { params }) {
  try {
    const { chatId } = params;
    const { content, type } = await request.json();
    
    if (!chatId || !content || !type) {
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      );
    }

    const messageData = {
      content,
      type,
      timestamp: serverTimestamp()
    };

    const docRef = await addDoc(
      collection(db, 'chats', chatId, 'messages'), 
      messageData
    );
    
    return NextResponse.json({
      success: true,
      id: docRef.id,
      ...messageData
    });
  } catch (error) {
    console.error('Error creating message:', error);
    return NextResponse.json(
      { error: 'Failed to create message' },
      { status: 500 }
    );
  }
}