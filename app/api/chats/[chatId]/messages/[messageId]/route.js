import { NextResponse } from 'next/server';
import { 
  getDoc, 
  updateDoc, 
  deleteDoc, 
  doc 
} from 'firebase/firestore';
import { db } from '@/lib/firebase';

// GET a specific message
export async function GET(request, { params }) {
  try {
    const { chatId, messageId } = params;
    
    if (!chatId || !messageId) {
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      );
    }

    const messageDoc = await getDoc(doc(db, 'chats', chatId, 'messages', messageId));
    
    if (!messageDoc.exists()) {
      return NextResponse.json(
        { error: 'Message not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      id: messageDoc.id,
      ...messageDoc.data()
    });
  } catch (error) {
    console.error('Error fetching message:', error);
    return NextResponse.json(
      { error: 'Failed to fetch message' },
      { status: 500 }
    );
  }
}

// PUT update a message
export async function PUT(request, { params }) {
  try {
    const { chatId, messageId } = params;
    const { content } = await request.json();
    
    if (!chatId || !messageId) {
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      );
    }

    const messageRef = doc(db, 'chats', chatId, 'messages', messageId);
    const messageDoc = await getDoc(messageRef);
    
    if (!messageDoc.exists()) {
      return NextResponse.json(
        { error: 'Message not found' },
        { status: 404 }
      );
    }

    await updateDoc(messageRef, { content });
    
    return NextResponse.json({
      success: true,
      id: messageId
    });
  } catch (error) {
    console.error('Error updating message:', error);
    return NextResponse.json(
      { error: 'Failed to update message' },
      { status: 500 }
    );
  }
}

// DELETE a message
export async function DELETE(request, { params }) {
  try {
    const { chatId, messageId } = params;
    
    if (!chatId || !messageId) {
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      );
    }

    const messageRef = doc(db, 'chats', chatId, 'messages', messageId);
    const messageDoc = await getDoc(messageRef);
    
    if (!messageDoc.exists()) {
      return NextResponse.json(
        { error: 'Message not found' },
        { status: 404 }
      );
    }

    await deleteDoc(messageRef);
    
    return NextResponse.json({
      success: true,
      id: messageId
    });
  } catch (error) {
    console.error('Error deleting message:', error);
    return NextResponse.json(
      { error: 'Failed to delete message' },
      { status: 500 }
    );
  }
}