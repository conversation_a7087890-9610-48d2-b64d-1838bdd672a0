import { NextResponse } from 'next/server';
import { 
  getDoc, 
  updateDoc, 
  deleteDoc, 
  doc, 
  serverTimestamp 
} from 'firebase/firestore';
import { db } from '@/lib/firebase';

// GET a specific chat
export async function GET(request, { params }) {
  try {
    const { chatId } = params;
    
    if (!chatId) {
      return NextResponse.json(
        { error: 'Missing chat ID' },
        { status: 400 }
      );
    }

    const chatDoc = await getDoc(doc(db, 'chats', chatId));
    
    if (!chatDoc.exists()) {
      return NextResponse.json(
        { error: 'Chat not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      id: chatDoc.id,
      ...chatDoc.data()
    });
  } catch (error) {
    console.error('Error fetching chat:', error);
    return NextResponse.json(
      { error: 'Failed to fetch chat' },
      { status: 500 }
    );
  }
}

// PUT update a chat
export async function PUT(request, { params }) {
  try {
    const { chatId } = params;
    const { title, bookInfo } = await request.json();
    
    if (!chatId) {
      return NextResponse.json(
        { error: 'Missing chat ID' },
        { status: 400 }
      );
    }

    const chatRef = doc(db, 'chats', chatId);
    const chatDoc = await getDoc(chatRef);
    
    if (!chatDoc.exists()) {
      return NextResponse.json(
        { error: 'Chat not found' },
        { status: 404 }
      );
    }

    const updateData = {
      updatedAt: serverTimestamp()
    };

    if (title !== undefined) {
      updateData.title = title;
    }

    if (bookInfo !== undefined) {
      updateData.bookInfo = bookInfo;
    }

    await updateDoc(chatRef, updateData);
    
    return NextResponse.json({
      success: true,
      id: chatId
    });
  } catch (error) {
    console.error('Error updating chat:', error);
    return NextResponse.json(
      { error: 'Failed to update chat' },
      { status: 500 }
    );
  }
}

// DELETE a chat
export async function DELETE(request, { params }) {
  try {
    const { chatId } = params;
    
    if (!chatId) {
      return NextResponse.json(
        { error: 'Missing chat ID' },
        { status: 400 }
      );
    }

    const chatRef = doc(db, 'chats', chatId);
    const chatDoc = await getDoc(chatRef);
    
    if (!chatDoc.exists()) {
      return NextResponse.json(
        { error: 'Chat not found' },
        { status: 404 }
      );
    }

    await deleteDoc(chatRef);
    
    return NextResponse.json({
      success: true,
      id: chatId
    });
  } catch (error) {
    console.error('Error deleting chat:', error);
    return NextResponse.json(
      { error: 'Failed to delete chat' },
      { status: 500 }
    );
  }
}