import { NextResponse } from 'next/server';
import { 
  getDoc, 
  updateDoc, 
  deleteDoc, 
  doc 
} from 'firebase/firestore';
import { db } from '@/lib/firebase';

// GET a specific image
export async function GET(request, { params }) {
  try {
    const { chatId, imageId } = params;
    
    if (!chatId || !imageId) {
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      );
    }

    const imageDoc = await getDoc(doc(db, 'chats', chatId, 'images', imageId));
    
    if (!imageDoc.exists()) {
      return NextResponse.json(
        { error: 'Image not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      id: imageDoc.id,
      ...imageDoc.data()
    });
  } catch (error) {
    console.error('Error fetching image:', error);
    return NextResponse.json(
      { error: 'Failed to fetch image' },
      { status: 500 }
    );
  }
}

// PUT update an image
export async function PUT(request, { params }) {
  try {
    const { chatId, imageId } = params;
    const { url, prompt, nodeId } = await request.json();
    
    if (!chatId || !imageId) {
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      );
    }

    const imageRef = doc(db, 'chats', chatId, 'images', imageId);
    const imageDoc = await getDoc(imageRef);
    
    if (!imageDoc.exists()) {
      return NextResponse.json(
        { error: 'Image not found' },
        { status: 404 }
      );
    }

    const updateData = {};
    
    if (url !== undefined) updateData.url = url;
    if (prompt !== undefined) updateData.prompt = prompt;
    if (nodeId !== undefined) updateData.nodeId = nodeId;

    await updateDoc(imageRef, updateData);
    
    return NextResponse.json({
      success: true,
      id: imageId
    });
  } catch (error) {
    console.error('Error updating image:', error);
    return NextResponse.json(
      { error: 'Failed to update image' },
      { status: 500 }
    );
  }
}

// DELETE an image
export async function DELETE(request, { params }) {
  try {
    const { chatId, imageId } = params;
    
    if (!chatId || !imageId) {
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      );
    }

    const imageRef = doc(db, 'chats', chatId, 'images', imageId);
    const imageDoc = await getDoc(imageRef);
    
    if (!imageDoc.exists()) {
      return NextResponse.json(
        { error: 'Image not found' },
        { status: 404 }
      );
    }

    await deleteDoc(imageRef);
    
    return NextResponse.json({
      success: true,
      id: imageId
    });
  } catch (error) {
    console.error('Error deleting image:', error);
    return NextResponse.json(
      { error: 'Failed to delete image' },
      { status: 500 }
    );
  }
}