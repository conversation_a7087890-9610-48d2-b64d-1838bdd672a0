import { NextResponse } from 'next/server';
import { 
  collection, 
  addDoc, 
  getDocs, 
  query, 
  orderBy, 
  serverTimestamp 
} from 'firebase/firestore';
import { db } from '@/lib/firebase';

// GET all images for a chat
export async function GET(request, { params }) {
  try {
    const { chatId } = params;
    const { searchParams } = new URL(request.url);
    const nodeId = searchParams.get('nodeId');
    
    if (!chatId) {
      return NextResponse.json(
        { error: 'Missing chat ID' },
        { status: 400 }
      );
    }

    const imagesRef = collection(db, 'chats', chatId, 'images');
    let q;
    
    if (nodeId) {
      q = query(imagesRef, where('nodeId', '==', nodeId), orderBy('createdAt', 'desc'));
    } else {
      q = query(imagesRef, orderBy('createdAt', 'desc'));
    }
    
    const querySnapshot = await getDocs(q);
    
    const images = [];
    querySnapshot.forEach((doc) => {
      images.push({
        id: doc.id,
        ...doc.data()
      });
    });

    return NextResponse.json({ images });
  } catch (error) {
    console.error('Error fetching images:', error);
    return NextResponse.json(
      { error: 'Failed to fetch images' },
      { status: 500 }
    );
  }
}

// POST create a new image
export async function POST(request, { params }) {
  try {
    const { chatId } = params;
    const { url, prompt, nodeId } = await request.json();
    
    if (!chatId || !url) {
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      );
    }

    const imageData = {
      url,
      prompt: prompt || '',
      nodeId: nodeId || '',
      createdAt: serverTimestamp()
    };

    const docRef = await addDoc(
      collection(db, 'chats', chatId, 'images'), 
      imageData
    );
    
    return NextResponse.json({
      success: true,
      id: docRef.id,
      ...imageData
    });
  } catch (error) {
    console.error('Error creating image:', error);
    return NextResponse.json(
      { error: 'Failed to create image' },
      { status: 500 }
    );
  }
}