import { NextResponse } from 'next/server';
import { 
  getDoc, 
  setDoc, 
  doc, 
  serverTimestamp 
} from 'firebase/firestore';
import { db } from '@/lib/firebase';

// GET story data for a chat
export async function GET(request, { params }) {
  try {
    const { chatId } = params;
    
    if (!chatId) {
      return NextResponse.json(
        { error: 'Missing chat ID' },
        { status: 400 }
      );
    }

    const storyDataDoc = await getDoc(doc(db, 'chats', chatId, 'storyData', 'main'));
    
    if (!storyDataDoc.exists()) {
      return NextResponse.json(
        { error: 'Story data not found' },
        { status: 404 }
      );
    }

    const data = storyDataDoc.data();
    
    return NextResponse.json({
      id: storyDataDoc.id,
      ...data
    });
  } catch (error) {
    console.error('Error fetching story data:', error);
    return NextResponse.json(
      { error: 'Failed to fetch story data' },
      { status: 500 }
    );
  }
}

// POST create/update story data for a chat
export async function POST(request, { params }) {
  try {
    const { chatId } = params;
    const storyData = await request.json();
    
    if (!chatId) {
      return NextResponse.json(
        { error: 'Missing chat ID' },
        { status: 400 }
      );
    }

    if (!storyData.questions || !Array.isArray(storyData.questions)) {
      return NextResponse.json(
        { error: 'Invalid story data format - questions array required' },
        { status: 400 }
      );
    }

    // Prepare the data to be saved
    const dataToSave = {
      ...storyData,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      chatId: chatId
    };

    // Save to Firestore
    const storyDataRef = doc(db, 'chats', chatId, 'storyData', 'main');
    await setDoc(storyDataRef, dataToSave, { merge: true });
    
    return NextResponse.json({
      success: true,
      id: 'main',
      chatId: chatId,
      message: 'Story data saved successfully'
    });
  } catch (error) {
    console.error('Error saving story data:', error);
    return NextResponse.json(
      { error: 'Failed to save story data' },
      { status: 500 }
    );
  }
}

// PUT update existing story data
export async function PUT(request, { params }) {
  try {
    const { chatId } = params;
    const updateData = await request.json();
    
    if (!chatId) {
      return NextResponse.json(
        { error: 'Missing chat ID' },
        { status: 400 }
      );
    }

    const storyDataRef = doc(db, 'chats', chatId, 'storyData', 'main');
    const storyDataDoc = await getDoc(storyDataRef);
    
    if (!storyDataDoc.exists()) {
      return NextResponse.json(
        { error: 'Story data not found' },
        { status: 404 }
      );
    }

    // Prepare update data
    const dataToUpdate = {
      ...updateData,
      updatedAt: serverTimestamp()
    };

    await setDoc(storyDataRef, dataToUpdate, { merge: true });
    
    return NextResponse.json({
      success: true,
      id: 'main',
      chatId: chatId,
      message: 'Story data updated successfully'
    });
  } catch (error) {
    console.error('Error updating story data:', error);
    return NextResponse.json(
      { error: 'Failed to update story data' },
      { status: 500 }
    );
  }
}

// DELETE story data for a chat
export async function DELETE(request, { params }) {
  try {
    const { chatId } = params;
    
    if (!chatId) {
      return NextResponse.json(
        { error: 'Missing chat ID' },
        { status: 400 }
      );
    }

    const storyDataRef = doc(db, 'chats', chatId, 'storyData', 'main');
    const storyDataDoc = await getDoc(storyDataRef);
    
    if (!storyDataDoc.exists()) {
      return NextResponse.json(
        { error: 'Story data not found' },
        { status: 404 }
      );
    }

    await deleteDoc(storyDataRef);
    
    return NextResponse.json({
      success: true,
      id: 'main',
      chatId: chatId,
      message: 'Story data deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting story data:', error);
    return NextResponse.json(
      { error: 'Failed to delete story data' },
      { status: 500 }
    );
  }
}