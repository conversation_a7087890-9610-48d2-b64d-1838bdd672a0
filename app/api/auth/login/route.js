import { NextResponse } from 'next/server';
import { authConfig } from '@/lib/auth';
import { setAuthCookies } from 'next-firebase-auth-edge/next/cookies';
import { updateUserLastLogin } from '@/lib/database';
import { adminAuth } from '@/lib/firebase-admin';

export async function GET(req) {
    try {
        const authHeader = req.headers.get('authorization');
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return new NextResponse('Missing or invalid authorization header', { status: 401 });
        }
        
        const response = await setAuthCookies(req.headers, authConfig);
        return response;
    } catch (e) {
        return new NextResponse('Invalid token', { status: 401 });
    }
}

export async function POST(req) {
    try {
        const authHeader = req.headers.get('authorization');
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return new NextResponse('Missing or invalid authorization header', { status: 401 });
        }
        
        // Extract the token
        const token = authHeader.split('Bearer ')[1];
        
        // Verify the token to get the user ID
        const decodedToken = await adminAuth.verifyIdToken(token);
        const uid = decodedToken.uid;
        
        // Update the user's last login timestamp
        await updateUserLastLogin(uid);
        
        // Set auth cookies as before
        const response = await setAuthCookies(req.headers, authConfig);
        return response;
    } catch (e) {
        console.error('Login error:', e);
        return new NextResponse('Invalid token', { status: 401 });
    }
}
