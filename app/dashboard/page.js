'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { signOut } from 'firebase/auth';
import { auth } from '@/lib/firebase';
import { 
  ChevronRight, 
  Settings, 
  User, 
  Globe, 
  Shield 
} from 'lucide-react';

export default function DashboardPage() {
  const router = useRouter();
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const [activeSection, setActiveSection] = useState('chat');
  const [expandedMenus, setExpandedMenus] = useState({
    chatHistory: false,
    settings: false
  });
  
  // Add logout handler
  const handleLogout = async () => {
    try {
      // Call logout API to clear cookies
      await fetch('/api/auth/logout', {
        method: 'GET',
      });

      // Sign out from Firebase
      await signOut(auth);

      // Redirect to main page
      router.push('/register');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  // State for API data
  const [chats, setChats] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [currentUser, setCurrentUser] = useState(null);

  // Mock user ID - In a real app, this would come from authentication
  const userId = 'user123'; // Replace with actual user ID from auth context

  // Fetch user's chats on component mount
  useEffect(() => {
    fetchUserChats();
  }, []);

  const fetchUserChats = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/chats?userId=${userId}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch chats');
      }

      const data = await response.json();
      setChats(data.chats || []);
    } catch (err) {
      console.error('Error fetching chats:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const createNewChat = async () => {
    try {
      const response = await fetch('/api/chats', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: userId,
          title: 'New Story Analysis',
          bookInfo: {}
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to create chat');
      }

      const data = await response.json();
      
      // Refresh the chats list
      await fetchUserChats();
      
      // Navigate to the new chat
      router.push(`/chat/${data.id}`);
    } catch (err) {
      console.error('Error creating chat:', err);
      setError(err.message);
    }
  };

  const deleteChat = async (chatId) => {
    if (!confirm('Are you sure you want to delete this chat?')) {
      return;
    }

    try {
      const response = await fetch(`/api/chats/${chatId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete chat');
      }

      // Refresh the chats list
      await fetchUserChats();
    } catch (err) {
      console.error('Error deleting chat:', err);
      setError(err.message);
    }
  };

  const toggleMenu = (menu) => {
    setExpandedMenus(prev => ({
      ...prev,
      [menu]: !prev[menu]
    }));
  };

  const formatDate = (timestamp) => {
    if (!timestamp) return 'Unknown';
    
    // Handle Firestore timestamp
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const getChatProgress = (chat) => {
    // Calculate progress based on available data
    let progress = 0;
    if (chat.bookInfo && Object.keys(chat.bookInfo).length > 0) progress += 25;
    // You can add more progress indicators based on messages, story data, etc.
    return Math.min(progress, 100);
  };

  return (
    <div className="flex h-screen bg-[#2a2d32] overflow-hidden">
      {/* Sidebar */}
      <aside className={`${isSidebarOpen ? 'w-64' : 'w-16'} bg-[#1e2023] transition-all duration-300 flex flex-col`}>
        {/* Logo/Brand */}
        <div className="p-4 border-b border-[#3a3d42]">
          <Link href="/" className="flex items-center space-x-2">
            {isSidebarOpen ? (
              <h1 className="font-slackey text-2xl text-white">What-if</h1>
            ) : (
              <span className="font-slackey text-xl text-white">W</span>
            )}
          </Link>
        </div>

        {/* Navigation */}
        <nav className="flex-1 p-4 space-y-2">
          {/* New Chat Button */}
          <button
            onClick={createNewChat}
            className="w-full flex items-center space-x-3 px-3 py-2 rounded-md bg-[#8B5CF6] text-white hover:bg-[#7C3AED] transition-colors"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4" />
            </svg>
            {isSidebarOpen && <span className="font-inter text-sm">New Analysis</span>}
          </button>

          {/* Chat */}
          <button
            onClick={() => setActiveSection('chat')}
            className={`w-full flex items-center space-x-3 px-3 py-2 rounded-md transition-colors ${
              activeSection === 'chat' 
                ? 'bg-[#8B5CF6] text-white' 
                : 'text-[#696F79] hover:bg-[#3a3d42] hover:text-white'
            }`}
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" 
                d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
            {isSidebarOpen && <span className="font-inter text-sm">My Stories</span>}
          </button>

          {/* Chat History */}
          <div>
            <button
              onClick={() => toggleMenu('chatHistory')}
              className="w-full flex items-center justify-between px-3 py-2 rounded-md text-[#696F79] hover:bg-[#3a3d42] hover:text-white transition-colors"
            >
              <div className="flex items-center space-x-3">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" 
                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                {isSidebarOpen && <span className="font-inter text-sm">Recent</span>}
              </div>
              {isSidebarOpen && (
                <svg className={`w-4 h-4 transition-transform ${expandedMenus.chatHistory ? 'rotate-180' : ''}`} 
                  fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                </svg>
              )}
            </button>
            {expandedMenus.chatHistory && isSidebarOpen && (
              <div className="mt-2 ml-8 space-y-1">
                {chats.slice(0, 3).map((chat) => (
                  <button 
                    key={chat.id}
                    onClick={() => router.push(`/chat/${chat.id}`)}
                    className="w-full text-left px-3 py-1 text-sm text-[#696F79] hover:text-white transition-colors truncate"
                  >
                    {chat.title}
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Settings */}
          <div className="mt-2">
            <button
              onClick={() => setExpandedMenus(prev => ({
                ...prev,
                settings: !prev.settings
              }))}
              className="w-full flex items-center justify-between p-2 hover:bg-[#3E3F43] rounded-lg transition-colors"
            >
              <div className="flex items-center space-x-2">
                <Settings className="w-4 h-4 text-gray-400" />
                <span className="text-sm text-gray-300">Settings</span>
              </div>
              <ChevronRight 
                className={`w-4 h-4 text-gray-400 transition-transform ${
                  expandedMenus.settings ? 'rotate-90' : ''
                }`} 
              />
            </button>
            
            {expandedMenus.settings && isSidebarOpen && (
              <div className="ml-6 mt-1 space-y-1">
                <Link 
                  href="/profile" 
                  className="flex items-center space-x-2 p-2 hover:bg-[#3a3d42] rounded-lg cursor-pointer transition-colors"
                >
                  <User className="w-3 h-3 text-gray-400" />
                  <span className="text-xs text-gray-300">Profile</span>
                </Link>
                <div className="flex items-center space-x-2 p-2 hover:bg-[#3E3F43] rounded-lg cursor-pointer transition-colors">
                  <Globe className="w-3 h-3 text-gray-400" />
                  <span className="text-xs text-gray-300">Language</span>
                </div>
                <div className="flex items-center space-x-2 p-2 hover:bg-[#3E3F43] rounded-lg cursor-pointer transition-colors">
                  <Shield className="w-3 h-3 text-gray-400" />
                  <span className="text-xs text-gray-300">Formality Level</span>
                </div>
              </div>
            )}
          </div>
        </nav>

        {/* Logout Button */}
        <div className="p-4 border-t border-[#3a3d42]">
          <button
            onClick={handleLogout}
            className="w-full flex items-center space-x-3 px-3 py-2 rounded-md text-[#696F79] hover:bg-[#3a3d42] hover:text-white transition-colors"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" 
                d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
            </svg>
            {isSidebarOpen && <span className="font-inter text-sm">Logout</span>}
          </button>
        </div>

        {/* Toggle Sidebar */}
        <div className="p-4 border-t border-[#3a3d42]">
          <button
            onClick={() => setIsSidebarOpen(!isSidebarOpen)}
            className="w-full flex items-center justify-center p-2 rounded-md text-[#696F79] hover:bg-[#3a3d42] hover:text-white transition-colors"
          >
            <svg className={`w-5 h-5 transition-transform ${isSidebarOpen ? '' : 'rotate-180'}`} 
              fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" 
                d="M11 19l-7-7 7-7m8 14l-7-7 7-7" />
            </svg>
          </button>
        </div>
      </aside>

      {/* Main Content */}
      <main className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <header className="bg-[#1e2023] border-b border-[#3a3d42] px-6 py-4">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold text-white font-inter">My Story Analyses</h2>
            <div className="flex items-center space-x-4">
              {/* Refresh Button */}
              <button 
                onClick={fetchUserChats}
                className="p-2 rounded-md text-[#696F79] hover:bg-[#3a3d42] hover:text-white transition-colors"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" 
                    d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
              </button>

              {/* User Avatar */}
              <button className="flex items-center space-x-2 p-1 rounded-full hover:bg-[#3a3d42] transition-colors">
                <div className="w-10 h-10 rounded-full bg-gradient-to-r from-[#8B5CF6] to-[#7C3AED] flex items-center justify-center">
                  <span className="text-white font-medium">U</span>
                </div>
              </button>
            </div>
          </div>
        </header>

        {/* Content Area */}
        <div className="flex-1 overflow-y-auto p-6">
          {/* Error Message */}
          {error && (
            <div className="mb-6 p-4 bg-red-500/10 border border-red-500/20 rounded-lg">
              <p className="text-red-400">Error: {error}</p>
              <button 
                onClick={() => setError(null)}
                className="text-sm text-red-300 hover:text-red-100 mt-2"
              >
                Dismiss
              </button>
            </div>
          )}

          {/* Loading State */}
          {loading ? (
            <div className="flex items-center justify-center h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#8B5CF6]"></div>
            </div>
          ) : (
            <>
              {/* Stats Row */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <div className="bg-[#1e2023] rounded-lg p-6">
                  <h3 className="text-[#696F79] text-sm font-medium">Total Stories</h3>
                  <p className="text-2xl font-bold text-white mt-2">{chats.length}</p>
                </div>
                <div className="bg-[#1e2023] rounded-lg p-6">
                  <h3 className="text-[#696F79] text-sm font-medium">This Month</h3>
                  <p className="text-2xl font-bold text-white mt-2">
                    {chats.filter(chat => {
                      const chatDate = chat.createdAt?.toDate ? chat.createdAt.toDate() : new Date(chat.createdAt);
                      const thisMonth = new Date();
                      return chatDate.getMonth() === thisMonth.getMonth() && 
                             chatDate.getFullYear() === thisMonth.getFullYear();
                    }).length}
                  </p>
                </div>
                <div className="bg-[#1e2023] rounded-lg p-6">
                  <h3 className="text-[#696F79] text-sm font-medium">Completed</h3>
                  <p className="text-2xl font-bold text-white mt-2">
                    {chats.filter(chat => getChatProgress(chat) === 100).length}
                  </p>
                </div>
              </div>

              {/* Chat Grid */}
              {chats.length === 0 ? (
                <div className="text-center py-12">
                  <div className="w-24 h-24 mx-auto mb-6 rounded-full bg-[#3a3d42] flex items-center justify-center">
                    <svg className="w-12 h-12 text-[#696F79]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" 
                        d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-semibold text-white mb-2">No story analyses yet</h3>
                  <p className="text-[#696F79] mb-6">Start by uploading a PDF and creating your first alternate timeline story!</p>
                  <button
                    onClick={createNewChat}
                    className="px-6 py-3 bg-[#8B5CF6] text-white rounded-lg hover:bg-[#7C3AED] transition-colors font-medium"
                  >
                    Create Your First Analysis
                  </button>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                  {chats.map((chat) => (
                    <div key={chat.id} className="bg-[#1e2023] rounded-lg overflow-hidden hover:shadow-lg transition-shadow group">
                      {/* Chat Header */}
                      <div className="relative h-48 bg-gradient-to-br from-[#8B5CF6] to-[#7C3AED] flex items-center justify-center">
                        <div className="text-center">
                          <svg className="w-16 h-16 text-white/70 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" 
                              d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                          </svg>
                          <p className="text-white/90 text-sm">{formatDate(chat.createdAt)}</p>
                        </div>
                        
                        {/* Delete Button */}
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            deleteChat(chat.id);
                          }}
                          className="absolute top-2 right-2 p-2 rounded-full bg-red-500/20 text-red-300 hover:bg-red-500/30 opacity-0 group-hover:opacity-100 transition-opacity"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                          </svg>
                        </button>
                      </div>

                      {/* Chat Content */}
                      <div className="p-4 space-y-3">
                        <h4 className="font-inter font-semibold text-white text-lg leading-tight">
                          {chat.title}
                        </h4>
                        
                        {chat.bookInfo?.bookTitle && (
                          <p className="text-[#696F79] text-sm">
                            Book: {chat.bookInfo.bookTitle}
                          </p>
                        )}
                        
                        {chat.bookInfo?.author && (
                          <p className="text-[#696F79] text-sm">
                            Author: {chat.bookInfo.author}
                          </p>
                        )}
                        
                        {/* Progress Bar */}
                        <div className="space-y-2">
                          <div className="flex justify-between text-xs text-[#696F79]">
                            <span>Progress</span>
                            <span>{getChatProgress(chat)}%</span>
                          </div>
                          <div className="w-full bg-[#3a3d42] rounded-full h-2">
                            <div 
                              className="bg-[#8B5CF6] h-2 rounded-full transition-all duration-300"
                              style={{ width: `${getChatProgress(chat)}%` }}
                            />
                          </div>
                        </div>

                        {/* Continue Button */}
                        <button
                          onClick={() => router.push(`/chat/${chat.id}`)}
                          className="w-full py-2.5 px-4 bg-[#8B5CF6] text-white rounded-md font-medium hover:bg-[#7C3AED] transition-colors duration-200 flex items-center justify-center space-x-2 text-sm font-inter"
                        >
                          <span>Continue Analysis</span>
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" 
                              d="M13 7l5 5m0 0l-5 5m5-5H6" />
                          </svg>
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </>
          )}
        </div>
      </main>
    </div>
  );
}
