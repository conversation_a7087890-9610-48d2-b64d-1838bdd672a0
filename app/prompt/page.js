'use client';
import { useState, useEffect, useRef, useCallback } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import {
  generateEmbeddingsForChunks,
  rankChunksByRelevance
} from '../utils/embeddingAnalysis';
import {
  loadPDFDocument,
  getPDFOutline,
  processOutlineItems,
  createPDFChunks
} from '../utils/pdfUtils';

export default function Home() {
  const [isClient, setIsClient] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [prompt, setPrompt] = useState('');
  const [file, setFile] = useState(null);
  const [fileName, setFileName] = useState('');
  const [error, setError] = useState('');
  const [outlineData, setOutlineData] = useState(null);
  const [totalPages, setTotalPages] = useState(null);
  const [pdfChunks, setPdfChunks] = useState(null);
  const [chunksWithEmbeddings, setChunksWithEmbeddings] = useState(null);
  const [isGeneratingEmbeddings, setIsGeneratingEmbeddings] = useState(false);
  const [embeddingsGenerated, setEmbeddingsGenerated] = useState(false);
  const [embeddingProgress, setEmbeddingProgress] = useState({ current: 0, total: 0, chunkId: '' });
  const [analysisResults, setAnalysisResults] = useState(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const fileInputRef = useRef(null);
  const [pdfInstance, setPdfInstance] = useState(null);

  // New state for automated workflow and API integration
  const [isProcessingWorkflow, setIsProcessingWorkflow] = useState(false);
  const [workflowStep, setWorkflowStep] = useState('');
  const [workflowProgress, setWorkflowProgress] = useState(0);
  const [currentChatId, setCurrentChatId] = useState(null);
  const [isSavingToAPI, setIsSavingToAPI] = useState(false);
  const router = useRouter();

  // Mock user ID - In a real app, this would come from authentication
  const userId = 'user123'; // Replace with actual user ID from auth context

  // Function to extract text from a page
  const extractTextFromPage = async (pageNum) => {
    if (!pdfInstance) return '';

    try {
      const page = await pdfInstance.getPage(pageNum);
      const textContent = await page.getTextContent();
      return textContent.items.map(item => item.str).join(' ');
    } catch (error) {
      console.error(`Error extracting text from page ${pageNum}:`, error);
      return '';
    }
  };

  // Function to extract text from a range of pages
  const extractTextFromPageRange = async (startPage, endPage) => {
    if (!pdfInstance) return '';

    const textPromises = [];
    for (let i = startPage; i <= endPage; i++) {
      textPromises.push(extractTextFromPage(i));
    }
    const pageTexts = await Promise.all(textPromises);
    return pageTexts.join('\n\n');
  };

  useEffect(() => {
    setIsClient(true);
  }, []);

  // Create a new chat session
  const createChatSession = async (bookTitle = 'Unknown Book', author = 'Unknown Author') => {
    try {
      const response = await fetch('/api/chats', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: userId,
          title: `${bookTitle} - Story Analysis`,
          bookInfo: {
            bookTitle,
            author,
            fileName: fileName,
            uploadedAt: new Date().toISOString()
          }
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to create chat session');
      }

      const data = await response.json();
      console.log('Chat session created:', data.id);
      return data.id;
    } catch (error) {
      console.error('Error creating chat session:', error);
      throw error;
    }
  };

  // Save analysis results to chat
  const saveAnalysisResults = async (chatId, results, prompt) => {
    try {
      setIsSavingToAPI(true);

      // Save the analysis results as a message
      const messageResponse = await fetch(`/api/chats/${chatId}/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: prompt,
          type: 'user_query'
        }),
      });

      if (!messageResponse.ok) {
        throw new Error('Failed to save user message');
      }

      // Save analysis results as another message
      const analysisResponse = await fetch(`/api/chats/${chatId}/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: JSON.stringify(results),
          type: 'analysis_results'
        }),
      });

      if (!analysisResponse.ok) {
        throw new Error('Failed to save analysis results');
      }

      console.log('Analysis results saved to chat');
    } catch (error) {
      console.error('Error saving analysis results:', error);
      throw error;
    } finally {
      setIsSavingToAPI(false);
    }
  };

  // Update chat with extracted book information
  const updateChatWithBookInfo = async (chatId, bookInfo) => {
    try {
      const response = await fetch(`/api/chats/${chatId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          bookInfo: {
            ...bookInfo,
            totalPages: totalPages,
            chunksCount: pdfChunks?.length || 0,
            processedAt: new Date().toISOString()
          }
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update chat with book info');
      }

      console.log('Chat updated with book information');
    } catch (error) {
      console.error('Error updating chat with book info:', error);
      // Don't throw here, as it's not critical for the main flow
    }
  };

  const handlePromptChange = (e) => {
    setPrompt(e.target.value);
  };

  const handleFileChange = (e) => {
    const selectedFile = e.target.files[0];
    if (selectedFile) {
      if (selectedFile.type !== 'application/pdf') {
        setError('Please upload a PDF file');
        setFile(null);
        setFileName('');
        return;
      }

      if (selectedFile.size > 30 * 1024 * 1024) { // 30MB limit
        setError('File size should be less than 30MB');
        setFile(null);
        setFileName('');
        return;
      }

      setFile(selectedFile);
      setFileName(selectedFile.name);
      setError('');
      setOutlineData(null);
      setTotalPages(null);
      setPdfChunks(null);
      setCurrentChatId(null); // Reset chat ID when new file is selected
    }
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const droppedFile = e.dataTransfer.files[0];

      if (droppedFile.type !== 'application/pdf') {
        setError('Please upload a PDF file');
        return;
      }

      if (droppedFile.size > 30 * 1024 * 1024) { // 30MB limit
        setError('File size should be less than 30MB');
        return;
      }

      setFile(droppedFile);
      setFileName(droppedFile.name);
      setError('');
      setOutlineData(null);
      setTotalPages(null);
      setPdfChunks(null);
      setCurrentChatId(null); // Reset chat ID when new file is selected
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!file) {
      setError('Please upload a PDF file first');
      return;
    }

    if (!prompt || !prompt.trim()) {
      setError('Please enter a prompt to analyze');
      return;
    }

    setIsLoading(true);
    setError('');
    setAnalysisResults(null);
    setChunksWithEmbeddings(null);
    setEmbeddingsGenerated(false);

    try {
      // Step 1: Create chat session first
      setWorkflowStep('Creating analysis session...');
      const chatId = await createChatSession(fileName.replace('.pdf', ''), 'Unknown Author');
      setCurrentChatId(chatId);

      // Step 2: Load PDF using utility function
      setWorkflowStep('Loading PDF document...');
      const pdf = await loadPDFDocument(file);
      setPdfInstance(pdf);
      setTotalPages(pdf.numPages);

      // Step 3: Extract outline
      setWorkflowStep('Extracting document structure...');
      const outline = await pdf.getOutline();
      console.log('Outline:', outline);

      // Process outline to extract page numbers
      const processOutlineItems = async (items) => {
        const result = [];

        for (const item of items) {
          const processedItem = { ...item };

          // Extract page number from destination if available
          if (item.dest) {
            try {
              // If dest is a string, we need to resolve it first
              let destRef = item.dest;
              if (typeof destRef === 'string') {
                destRef = await pdf.getDestination(destRef);
              }

              // The first element in the destination array is the page reference
              if (Array.isArray(destRef) && destRef.length > 0) {
                const pageRef = destRef[0];
                // Get the page number (add 1 because PDF.js uses 0-based indices)
                const pageNum = await pdf.getPageIndex(pageRef) + 1;
                processedItem.pageNumber = pageNum;
              }
            } catch (error) {
              console.error('Error extracting page number:', error);
            }
          }

          // Process nested items recursively
          if (item.items && item.items.length > 0) {
            processedItem.items = await processOutlineItems(item.items);
          }

          result.push(processedItem);
        }

        return result;
      };

      // Set the outline data for display
      if (!outline || outline.length === 0) {
        console.log('No outline found in the PDF');
        setOutlineData({ items: [], message: 'No outline found in this PDF document. Creating default chunks of 20 pages each.' });

        try {
          // Create default chunks of 20 pages each when no TOC is found
          setWorkflowStep('Creating content chunks...');
          const chunkSize = 20;
          const defaultChunks = [];

          for (let i = 1; i <= pdf.numPages; i += chunkSize) {
            const startPage = i;
            const endPage = Math.min(i + chunkSize - 1, pdf.numPages);

            // Extract text content from the page range
            const content = await extractTextFromPageRange(startPage, endPage);

            defaultChunks.push({
              id: `default-chunk-${Math.floor(i / chunkSize) + 1}`,
              title: `Pages ${startPage}-${endPage}`,
              path: [`Pages ${startPage}-${endPage}`],
              startPage: startPage,
              endPage: endPage,
              pageCount: endPage - startPage + 1,
              isDefaultChunk: true,
              content: content
            });
          }

          setPdfChunks(defaultChunks);
          console.log('Created default chunks successfully:', defaultChunks.length);

          // Store chunks for immediate use in workflow
          window.currentChunks = defaultChunks;
        } catch (chunkError) {
          console.error('Error creating default chunks:', chunkError);
          setError(`Error creating default chunks: ${chunkError.message}`);
        }
      } else {
        const processedOutline = await processOutlineItems(outline);
        setOutlineData({ items: processedOutline, message: '' });

        // Create PDF chunks based on the deepest sections in the outline
        const createPdfChunks = async (outlineItems) => {
          // Find all sections at the deepest level with page numbers
          const deepestSections = [];

          const findDeepestSections = (items, depth = 0, path = []) => {
            for (let i = 0; i < items.length; i++) {
              const item = items[i];
              const currentPath = [...path, item.title];

              if (item.items && item.items.length > 0) {
                // This item has children, go deeper
                findDeepestSections(item.items, depth + 1, currentPath);
              } else if (item.pageNumber) {
                // This is a leaf node with a page number
                deepestSections.push({
                  title: item.title,
                  pageNumber: item.pageNumber,
                  depth: depth,
                  path: currentPath
                });
              }
            }
          };

          findDeepestSections(outlineItems);

          // Sort sections by page number
          deepestSections.sort((a, b) => a.pageNumber - b.pageNumber);

          // Create chunks based on page ranges
          const chunks = [];
          for (let i = 0; i < deepestSections.length; i++) {
            const section = deepestSections[i];
            const nextSection = deepestSections[i + 1];

            const startPage = section.pageNumber;
            // If this is the last section, the end page is the total number of pages
            // Otherwise, it's the page before the next section starts
            const endPage = nextSection ? nextSection.pageNumber - 1 : pdf.numPages;

            // Extract text content from the page range
            const content = await extractTextFromPageRange(startPage, endPage);

            chunks.push({
              id: `chunk-${i + 1}`,
              title: section.title,
              path: section.path,
              startPage: startPage,
              endPage: endPage,
              pageCount: endPage - startPage + 1,
              content: content
            });
          }

          return chunks;
        };

        // Create chunks and handle async operation
        setWorkflowStep('Processing document sections...');
        const chunks = await createPdfChunks(processedOutline);

        // If no chunks were created (no deep sections with page numbers),
        // create default chunks based on total pages
        if (chunks.length === 0) {
          // Create chunks of approximately 20 pages each
          const chunkSize = 20;
          const defaultChunks = [];

          for (let i = 1; i <= pdf.numPages; i += chunkSize) {
            const startPage = i;
            const endPage = Math.min(i + chunkSize - 1, pdf.numPages);

            // Extract text content from the page range
            const content = await extractTextFromPageRange(startPage, endPage);

            defaultChunks.push({
              id: `default-chunk-${Math.floor(i / chunkSize) + 1}`,
              title: `Pages ${startPage}-${endPage}`,
              path: [`Pages ${startPage}-${endPage}`],
              startPage: startPage,
              endPage: endPage,
              pageCount: endPage - startPage + 1,
              isDefaultChunk: true,
              content: content
            });
          }

          setPdfChunks(defaultChunks);
          window.currentChunks = defaultChunks;
        } else {
          setPdfChunks(chunks);
          window.currentChunks = chunks;
        }
      }

      // Reset embeddings and analysis states
      setChunksWithEmbeddings(null);
      setEmbeddingsGenerated(false);
      setAnalysisResults(null);

      setIsLoading(false); // End PDF processing loading

      // Update chat with book information
      if (currentChatId || chatId) {
        await updateChatWithBookInfo(chatId, {
          bookTitle: fileName.replace('.pdf', ''),
          author: 'Unknown Author',
          fileName: fileName
        });
      }

      // Run the workflow immediately since we're storing chunks in window
      setTimeout(async () => {
        await runAutomatedWorkflow(chatId);
      }, 100);

    } catch (error) {
      console.error('Error submitting form:', error);
      setError(`Error processing PDF: ${error.message}`);
      setPdfChunks(null);
      setChunksWithEmbeddings(null);
      setAnalysisResults(null);
      setIsLoading(false);
    }
  };

  const triggerFileInput = () => {
    fileInputRef.current.click();
  };

  // Function to manually generate embeddings
  const handleGenerateEmbeddings = async () => {
    // Use current chunks from state or from window if state hasn't updated yet
    const chunksToUse = pdfChunks || window.currentChunks;

    if (!chunksToUse || chunksToUse.length === 0) {
      setError('No PDF chunks available. Please process a PDF first.');
      return;
    }

    setIsGeneratingEmbeddings(true);
    setError('');
    setEmbeddingProgress({ current: 0, total: chunksToUse.length, chunkId: '' });

    try {
      console.log('Manually generating embeddings for chunks...', chunksToUse.length);

      // Progress callback function
      const onProgress = (current, total, chunkId) => {
        setEmbeddingProgress({ current, total, chunkId });
      };

      const embedded = await generateEmbeddingsForChunks(chunksToUse, onProgress);
      setChunksWithEmbeddings(embedded);
      setEmbeddingsGenerated(true);
      console.log('Embeddings generated successfully');

      // Return the embedded chunks for immediate use
      return embedded;
    } catch (error) {
      console.error('Error generating embeddings:', error);
      setError(`Error generating embeddings: ${error.message}`);
      throw error; // Re-throw so workflow can handle it
    } finally {
      setIsGeneratingEmbeddings(false);
      setEmbeddingProgress({ current: 0, total: 0, chunkId: '' });
    }
  };

  // Function to analyze the prompt against embeddings
  const handleAnalyzePrompt = async (embeddedChunks) => {
    if (!prompt || !prompt.trim()) {
      setError('Please enter a prompt to analyze.');
      return;
    }

    if (!embeddedChunks || embeddedChunks.length === 0) {
      setError('No embeddings available. Please generate embeddings first.');
      return;
    }

    setIsAnalyzing(true);
    setError('');

    try {
      const results = await rankChunksByRelevance(prompt, embeddedChunks);
      setAnalysisResults(results);

      // Save analysis results to the current chat
      if (currentChatId) {
        await saveAnalysisResults(currentChatId, results, prompt);
      }

      // Save analysis results and prompt to localStorage for use in the alternate scenario page
      try {
        localStorage.setItem('analysisResults', JSON.stringify(results));
        localStorage.setItem('userPrompt', prompt);
        localStorage.setItem('currentChatId', currentChatId || '');
      } catch (storageError) {
        console.error('Error saving data to localStorage:', storageError);
      }

      return results;
    } catch (error) {
      console.error('Error analyzing prompt:', error);
      setError(`Error analyzing prompt: ${error.message}`);
      throw error;
    } finally {
      setIsAnalyzing(false);
    }
  };

  // Automated workflow function that runs all steps sequentially
  const runAutomatedWorkflow = useCallback(async (chatId) => {
    if (!prompt || !prompt.trim()) {
      setError('Please enter a prompt to analyze.');
      return;
    }

    setIsProcessingWorkflow(true);
    setError('');

    try {
      // Check if we have chunks with content
      const chunksToUse = pdfChunks || window.currentChunks;
      console.log('Checking chunks before workflow:', chunksToUse?.length || 0);
      if (chunksToUse && chunksToUse.length > 0) {
        console.log('First chunk has content:', !!chunksToUse[0]?.content);
        console.log('First chunk content length:', chunksToUse[0]?.content?.length || 0);
      }

      // Step 1: Generate Embeddings (call existing function)
      setWorkflowStep('Generating embeddings for PDF chunks...');
      setWorkflowProgress(25);

      const embeddedChunks = await handleGenerateEmbeddings();

      // Step 2: Analyze Prompt (call existing function)
      setWorkflowStep('Analyzing prompt and ranking content...');
      setWorkflowProgress(70);

      let analysisResults = null;
      if (embeddedChunks && embeddedChunks.length > 0) {
        console.log('Using embedded chunks directly:', embeddedChunks.length);
        analysisResults = await handleAnalyzePrompt(embeddedChunks);
      } else {
        console.error('No embedded chunks returned from handleGenerateEmbeddings');
        setError('Failed to generate embeddings');
        return;
      }

      // Step 3: Navigate to game/scenario generation
      setWorkflowStep('Preparing your interactive story experience...');
      setWorkflowProgress(90);

      // Small delay to show the final step
      await new Promise(resolve => setTimeout(resolve, 1500));

      setWorkflowStep('Launching story generator...');
      setWorkflowProgress(100);

      // Small delay before navigation
      await new Promise(resolve => setTimeout(resolve, 500));

      // Navigate to the alternate scenario page or stay on chat page
      if (currentChatId || chatId) {
        router.push(`/chat/${currentChatId || chatId}`);
      } else {
        router.push('/alternate-scenario');
      }

    } catch (error) {
      console.error('Error in automated workflow:', error);
      setError(`Error in automated workflow: ${error.message}`);
    } finally {
      setIsProcessingWorkflow(false);
      setWorkflowStep('');
      setWorkflowProgress(0);
      setEmbeddingProgress({ current: 0, total: 0, chunkId: '' });
    }
  }, [prompt, router, currentChatId, handleGenerateEmbeddings, handleAnalyzePrompt, pdfChunks]);

  if (!isClient) {
    return null;
  }

  return (
    <div className="min-h-screen page-background">
      {/* Full-screen loading overlay */}
      {isProcessingWorkflow && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-8 max-w-md w-full mx-4 text-center">
            <div className="mb-6">
              <div className="animate-spin rounded-full h-16 w-16 border-b-4 border-primary-green mx-auto mb-4"></div>
              <h2 className="text-2xl font-bold text-gray-800 mb-2">Processing Your Analysis</h2>
              <p className="text-gray-600 mb-4">{workflowStep}</p>
            </div>

            {/* Progress bar */}
            <div className="w-full bg-gray-200 rounded-full h-3 mb-4">
              <div
                className="bg-primary-green h-3 rounded-full transition-all duration-500 ease-out"
                style={{ width: `${workflowProgress}%` }}
              ></div>
            </div>
            <p className="text-sm text-gray-500">{Math.round(workflowProgress)}% Complete</p>

            {/* Embedding progress details */}
            {embeddingProgress.total > 0 && (
              <div className="mt-4 text-sm text-gray-600">
                <p>Processing chunk {embeddingProgress.current} of {embeddingProgress.total}</p>
                {embeddingProgress.chunkId && (
                  <p className="text-xs text-gray-500 mt-1">Current: {embeddingProgress.chunkId}</p>
                )}
              </div>
            )}

            {/* Saving to API indicator */}
            {isSavingToAPI && (
              <div className="mt-4 text-sm text-blue-600">
                <p>💾 Saving to your account...</p>
              </div>
            )}

            <div className="mt-6 text-xs text-gray-400">
              Creating your personal story analysis session...
            </div>
          </div>
        </div>
      )}

      {/* Header */}
      <header className="bg-primary-green text-white p-4">
        <div className="container mx-auto flex justify-between items-center">
          <h1 className="font-slackey text-2xl">What-if</h1>
          <div className="flex items-center space-x-4">
            <Link 
              href="/dashboard" 
              className="px-4 py-2 bg-primary-green-light text-white rounded-md hover:bg-primary-green-dark transition-colors"
            >
              Dashboard
            </Link>
            <Link 
              href="/auth" 
              className="px-4 py-2 bg-white text-primary-green rounded-md hover:bg-gray-100 transition-colors"
            >
              Sign In
            </Link>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto py-8 px-4">
        <div className="max-w-2xl mx-auto">
          <h1 className="font-libre text-3xl font-bold mb-6 text-text-primary">AI-Powered Story Analysis</h1>
          <p className="text-gray-600 mb-8">Upload a PDF and ask &quot;what if&quot; questions to create alternate timeline stories powered by AI.</p>

          <div className="bg-white rounded-lg shadow-md p-6 mb-8">
            <form onSubmit={handleSubmit}>
              {/* Prompt Input */}
              <div className="mb-6">
                <label htmlFor="prompt" className="block text-gray-700 font-medium mb-2">
                  Enter your &quot; what if &quot; prompt
                </label>
                <textarea
                  id="prompt"
                  value={prompt}
                  onChange={handlePromptChange}
                  placeholder="What if the main character made a different choice in chapter 3?"
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-green focus:border-transparent min-h-[120px]"
                />
                <p className="text-sm text-gray-500 mt-2">
                  💡 Try asking about character decisions, plot twists, or alternate outcomes
                </p>
              </div>

              {/* File Upload */}
              <div className="mb-6">
                <label className="block text-gray-700 font-medium mb-2">
                  Upload Story PDF
                </label>
                <div
                  className={`border-2 border-dashed rounded-md p-8 text-center cursor-pointer transition-colors ${
                    error && !file ? 'border-error' : 'border-gray-300 hover:border-primary-green'
                  }`}
                  onDragOver={handleDragOver}
                  onDrop={handleDrop}
                  onClick={triggerFileInput}
                >
                  <input
                    type="file"
                    ref={fileInputRef}
                    onChange={handleFileChange}
                    accept="application/pdf"
                    className="hidden"
                  />

                  {!file ? (
                    <div>
                      <div className="flex justify-center mb-4">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                        </svg>
                      </div>
                      <p className="text-gray-600 mb-1">Drag and drop your story PDF here, or click to browse</p>
                      <p className="text-gray-500 text-sm">Maximum file size: 30MB • Supports novels, short stories, scripts</p>
                    </div>
                  ) : (
                    <div className="flex items-center justify-center space-x-4">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-primary-green" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                      <div className="text-left">
                        <p className="text-primary-green font-medium">{fileName}</p>
                        <p className="text-sm text-gray-500">
                          {totalPages ? `${totalPages} pages` : 'Ready to analyze'}
                        </p>
                        <button
                          type="button"
                          className="text-sm text-gray-500 hover:text-error"
                          onClick={(e) => {
                            e.stopPropagation();
                            setFile(null);
                            setFileName('');
                            setOutlineData(null);
                            setTotalPages(null);
                            setPdfChunks(null);
                            setCurrentChatId(null);
                          }}
                        >
                          Remove
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Current Chat Info */}
              {currentChatId && (
                <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
                  <p className="text-blue-800 text-sm">
                    📝 Analysis session created • ID: {currentChatId}
                  </p>
                </div>
              )}

              {/* Error Message */}
              {error && (
                <div className="mb-4 p-3 bg-error/10 text-error rounded-md">
                  <p className="font-medium">Error:</p>
                  <p>{error}</p>
                  <button 
                    onClick={() => setError('')}
                    className="text-sm underline mt-1 hover:no-underline"
                  >
                    Dismiss
                  </button>
                </div>
              )}

              {/* Submit Button */}
              <button
                type="submit"
                disabled={isLoading || isProcessingWorkflow || !prompt.trim() || !file}
                className={`w-full px-4 py-3 rounded-md font-medium transition-all duration-300 ${
                  isLoading || isProcessingWorkflow || !prompt.trim() || !file
                    ? 'bg-gray-400 cursor-not-allowed'
                    : 'bg-primary-green text-white hover:bg-primary-green-dark shadow-lg hover:shadow-xl'
                }`}
              >
                {isLoading || isProcessingWorkflow ? (
                  <span className="flex items-center justify-center">
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    {isProcessingWorkflow
                      ? 'Creating Story Analysis...'
                      : isGeneratingEmbeddings
                        ? 'Generating Embeddings...'
                        : isAnalyzing
                          ? 'Analyzing Content...'
                          : 'Processing PDF...'}
                  </span>
                ) : (
                  'Analyze Story & Create Timeline'
                )}
              </button>

              {/* Processing Status */}
              {isLoading && (
                <div className="mt-4 text-sm text-gray-600">
                  <div className="flex items-center">
                    <div className="w-full bg-gray-200 rounded-full h-2.5 mr-2">
                      <div className="bg-primary-green h-2.5 rounded-full animate-pulse" style={{
                        width: isGeneratingEmbeddings
                          ? '75%'
                          : isAnalyzing
                            ? '90%'
                            : '50%'
                      }}></div>
                    </div>
                    <span className="whitespace-nowrap">
                      {isGeneratingEmbeddings
                        ? 'Generating embeddings...'
                        : isAnalyzing
                          ? 'Analyzing content...'
                          : 'Extracting PDF content...'}
                    </span>
                  </div>
                </div>
              )}
            </form>
          </div>

          {/* Success State */}
          {analysisResults && !isProcessingWorkflow && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-6 mb-8">
              <h3 className="text-lg font-semibold text-green-800 mb-2">✅ Analysis Complete!</h3>
              <p className="text-green-700 mb-4">
                Found {analysisResults.length} relevant sections. Your story analysis has been saved to your account.
              </p>
              <div className="flex space-x-4">
                <Link 
                  href={`/chat/${currentChatId}`}
                  className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
                >
                  View Analysis
                </Link>
                <Link 
                  href="/dashboard"
                  className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
                >
                  Go to Dashboard
                </Link>
              </div>
            </div>
          )}

          {/* How it Works */}
          <div className="bg-gray-50 rounded-lg p-6">
            <h3 className="text-xl font-semibold text-gray-800 mb-4">How It Works</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center">
                <div className="w-12 h-12 bg-primary-green rounded-full flex items-center justify-center mx-auto mb-2">
                  <span className="text-white font-bold">1</span>
                </div>
                <h4 className="font-medium text-gray-800 mb-1">Upload & Analyze</h4>
                <p className="text-sm text-gray-600">AI analyzes your story structure and key plot points</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-primary-green rounded-full flex items-center justify-center mx-auto mb-2">
                  <span className="text-white font-bold">2</span>
                </div>
                <h4 className="font-medium text-gray-800 mb-1">Ask &quot;What If&quot;</h4>
                <p className="text-sm text-gray-600">Pose questions about alternate character choices or outcomes</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-primary-green rounded-full flex items-center justify-center mx-auto mb-2">
                  <span className="text-white font-bold">3</span>
                </div>
                <h4 className="font-medium text-gray-800 mb-1">Explore Timelines</h4>
                <p className="text-sm text-gray-600">Discover interactive alternate scenarios and endings</p>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}