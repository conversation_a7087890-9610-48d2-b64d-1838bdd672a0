import Script from 'next/script';
import { libreBaskerville, figTree, slackey, inter } from '../style_vars';
import "../globals.css";

export const metadata = {
  title: "Coming Soon - The Money Tales",
  description: "We are crafting something cool. Join our waiting list now.",
};

export default async function RootLayout({ children }) {

  return (
    <html lang="en">
      <head>
        {/* EARLY Module declaration before engine loads */}
        <Script
          id="godot-module-setup"
          strategy="beforeInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              window.Module = {
                locateFile: function(path) {
                  return "/" + path;
                }
              };
            `,
          }}
        />
      </head>
      <body
        className={`${libreBaskerville.variable} ${slackey.variable} ${figTree.variable} ${inter.variable} bg-app-pattern`}
      >
        {children}
        {/* Load engine script */}
        <Script
          src="/index.js"
          strategy="beforeInteractive"
        />
      </body>
    </html>
  );
}
