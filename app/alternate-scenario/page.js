'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { extractBookInformation } from '../utils/embeddingAnalysis';
import { ArrowLeft, Save, RefreshCw } from 'lucide-react';

export default function AlternateScenarioPage() {
  const router = useRouter();
  const [isGenerating, setIsGenerating] = useState(true);
  const [gameData, setGameData] = useState(null);
  const [error, setError] = useState('');
  const [userPrompt, setUserPrompt] = useState('');
  const [bookInfo, setBookInfo] = useState({
    bookTitle: '',
    author: '',
    changeLocation: '',
    originalEvent: ''
  });

  // API integration state
  const [currentChatId, setCurrentChatId] = useState(null);
  const [analysisResults, setAnalysisResults] = useState(null);
  const [isLoadingFromAPI, setIsLoadingFromAPI] = useState(false);
  const [isSavingStoryData, setIsSavingStoryData] = useState(false);
  const [storyDataId, setStoryDataId] = useState(null);
  const [hasExistingStoryData, setHasExistingStoryData] = useState(false);

  // Check if we're in the browser
  const [isClient, setIsClient] = useState(false);
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Canvas-related state and refs
  const canvasRef = useRef(null);
  const containerRef = useRef(null);
  const [gameLoaded, setGameLoaded] = useState(false);
  const [loadingProgress, setLoadingProgress] = useState(0);
  const [gameError, setGameError] = useState(null);
  const engineRef = useRef(null);

  // Load chat data from API
  const loadChatData = useCallback(async (chatId) => {
    try {
      setIsLoadingFromAPI(true);
      
      // Fetch chat messages to get analysis results
      const messagesResponse = await fetch(`/api/chats/${chatId}/messages`);
      if (!messagesResponse.ok) {
        throw new Error('Failed to load chat messages');
      }
      
      const messagesData = await messagesResponse.json();
      const messages = messagesData.messages || [];
      
      // Find analysis results and user prompt
      const userQuery = messages.find(msg => msg.type === 'user_query');
      const analysisMessage = messages.find(msg => msg.type === 'analysis_results');
      
      if (userQuery) {
        setUserPrompt(userQuery.content);
      }
      
      if (analysisMessage) {
        try {
          const parsedResults = JSON.parse(analysisMessage.content);
          setAnalysisResults(parsedResults);
          
          // Extract book information
          const extractedBookInfo = extractBookInformation(parsedResults);
          setBookInfo(extractedBookInfo);
          
          console.log('Loaded analysis results from chat:', parsedResults.length, 'results');
        } catch (parseError) {
          console.error('Error parsing analysis results:', parseError);
          throw new Error('Invalid analysis results format');
        }
      }
      
      // Check if story data already exists
      try {
        const storyDataResponse = await fetch(`/api/chats/${chatId}/storyData`);
        if (storyDataResponse.ok) {
          const existingStoryData = await storyDataResponse.json();
          if (existingStoryData && existingStoryData.questions) {
            console.log('Found existing story data, loading...');
            setGameData(existingStoryData);
            setHasExistingStoryData(true);
            setIsGenerating(false);
            
            // Store in localStorage for Godot game
            localStorage.setItem('storyGameData', JSON.stringify(existingStoryData));
            return; // Skip generation if we have existing data
          }
        }
      } catch (storyDataError) {
        console.log('No existing story data found, will generate new');
      }
      
    } catch (error) {
      console.error('Error loading chat data:', error);
      setError(`Failed to load analysis data: ${error.message}`);
      setIsGenerating(false);
    } finally {
      setIsLoadingFromAPI(false);
    }
  }, []);

  // Save story data to chat
  const saveStoryDataToChat = useCallback(async (chatId, storyData) => {
    try {
      setIsSavingStoryData(true);
      
      const response = await fetch(`/api/chats/${chatId}/storyData`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...storyData,
          generatedAt: new Date().toISOString(),
          prompt: userPrompt,
          bookInfo: bookInfo
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to save story data');
      }

      const savedData = await response.json();
      setStoryDataId(savedData.id);
      console.log('Story data saved successfully:', savedData.id);
      
    } catch (error) {
      console.error('Error saving story data:', error);
      // Don't show error to user as this is not critical for gameplay
    } finally {
      setIsSavingStoryData(false);
    }
  }, [userPrompt, bookInfo]);

  // Function to generate the scenario for the game
  const generateScenario = useCallback(async (bookData, whatIfPrompt) => {
    try {
      // Ensure we have the required data
      if (!bookData.bookTitle || !bookData.author || !whatIfPrompt) {
        setError('Missing required information to generate scenario.');
        setIsGenerating(false);
        return;
      }

      console.log('Generating scenario with:', { bookData, whatIfPrompt });

      // Generate the game data using the API endpoint
      const response = await fetch('/api/alternate-scenario-game-data', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          bookTitle: bookData.bookTitle,
          author: bookData.author,
          changeLocation: bookData.changeLocation,
          whatIfPrompt: whatIfPrompt,
          originalEvent: bookData.originalEvent
        }),
      });

      const result = await response.json();

      if (response.ok && result.questions) {
        setGameData(result);
        
        // Store the game data in localStorage for the Godot game to access
        localStorage.setItem('storyGameData', JSON.stringify(result));
        
        // Save to chat if we have a chat ID
        if (currentChatId) {
          await saveStoryDataToChat(currentChatId, result);
        }
        
        console.log('Scenario generated successfully');
      } else {
        setError(result.error || 'Failed to generate story data');
      }
    } catch (error) {
      console.error('Error generating scenario:', error);
      setError(error.message || 'An unexpected error occurred');
    } finally {
      setIsGenerating(false);
    }
  }, [currentChatId, saveStoryDataToChat]);

  // Load data and generate scenario
  useEffect(() => {
    if (!isClient) return;

    async function loadDataAndGenerateScenario() {
      try {
        // First try to get chat ID from URL or localStorage
        let chatId = null;
        
        // Check URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const urlChatId = urlParams.get('chatId');
        
        // Check localStorage
        const savedChatId = localStorage.getItem('currentChatId');
        
        chatId = urlChatId || savedChatId;
        
        if (chatId && chatId !== '') {
          console.log('Loading data from chat:', chatId);
          setCurrentChatId(chatId);
          await loadChatData(chatId);
          
          // If we already have story data, don't generate new
          if (hasExistingStoryData) {
            return;
          }
        } else {
          // Fallback to localStorage method
          console.log('No chat ID found, using localStorage fallback');
          const savedResults = localStorage.getItem('analysisResults');
          const savedPrompt = localStorage.getItem('userPrompt');

          if (!savedResults || !savedPrompt) {
            setError('No analysis results found. Please analyze a PDF first.');
            setIsGenerating(false);
            return;
          }

          const parsedResults = JSON.parse(savedResults);
          setAnalysisResults(parsedResults);
          setUserPrompt(savedPrompt);

          // Extract book information from analysis results
          const extractedBookInfo = extractBookInformation(parsedResults);
          setBookInfo(extractedBookInfo);
        }

        // Generate scenario if we have the required data
        if ((analysisResults || bookInfo.bookTitle) && userPrompt) {
          await generateScenario(bookInfo, userPrompt);
        }
        
      } catch (error) {
        console.error('Error loading data and generating scenario:', error);
        setError('Error loading data: ' + (error.message || 'Unknown error'));
        setIsGenerating(false);
      }
    }

    loadDataAndGenerateScenario();
  }, [isClient, generateScenario, loadChatData, analysisResults, bookInfo, userPrompt, hasExistingStoryData]);

  // Regenerate scenario function
  const regenerateScenario = useCallback(async () => {
    if (!bookInfo.bookTitle || !userPrompt) {
      setError('Missing required data to regenerate scenario');
      return;
    }
    
    setIsGenerating(true);
    setError('');
    setGameData(null);
    setGameLoaded(false);
    
    await generateScenario(bookInfo, userPrompt);
  }, [bookInfo, userPrompt, generateScenario]);

  // Game initialization effect
  useEffect(() => {
    if (!gameData || !canvasRef.current || !containerRef.current) return;

    // Set canvas to maintain aspect ratio and fit within container
    const updateCanvasSize = () => {
      if (canvasRef.current && containerRef.current) {
        const container = containerRef.current;
        const rect = container.getBoundingClientRect();

        // Add some padding to ensure it fits comfortably
        const padding = 20;
        const availableWidth = Math.max(rect.width - padding, 300);
        const availableHeight = Math.max(rect.height - padding, 200);

        // Maintain 16:9 aspect ratio
        const aspectRatio = 16/9;

        let width = availableWidth;
        let height = width / aspectRatio;

        // If height is too large, constrain by height instead
        if (height > availableHeight) {
          height = availableHeight;
          width = height * aspectRatio;
        }

        // Ensure minimum viable size
        const minWidth = 400;
        const minHeight = 225; // 400/16*9

        if (width < minWidth) {
          width = Math.min(minWidth, availableWidth);
          height = width / aspectRatio;
        }

        if (height < minHeight) {
          height = Math.min(minHeight, availableHeight);
          width = height * aspectRatio;
        }

        // Final bounds check
        width = Math.min(width, availableWidth);
        height = Math.min(height, availableHeight);

        // Set the internal resolution (this affects game rendering quality)
        canvasRef.current.width = 1920; // Fixed internal resolution
        canvasRef.current.height = 1080; // Fixed internal resolution

        // Set the display size
        canvasRef.current.style.width = `${Math.floor(width)}px`;
        canvasRef.current.style.height = `${Math.floor(height)}px`;
        canvasRef.current.style.maxWidth = '100%';
        canvasRef.current.style.maxHeight = '100%';
        canvasRef.current.style.objectFit = 'contain';

        console.log(`Canvas sized to: ${Math.floor(width)}x${Math.floor(height)} (container: ${rect.width}x${rect.height})`);
      }
    };

    updateCanvasSize();
    window.addEventListener('resize', updateCanvasSize);

    let engine = null;

    // ✅ Define Module FIRST — critical
    window.Module = {
      locateFile: (path) => `/${path}`
    };

    const initializeGame = () => {
      const GODOT_CONFIG = {
        executable: 'index',
        canvasResizePolicy: 0, // 0 = None - we handle sizing ourselves
        fileSizes: {
          'index.pck': 97008,
          'index.wasm': 49282035
        },
        focusCanvas: true
      };

      engine = new window.Engine(GODOT_CONFIG);
      engineRef.current = engine;

      engine.startGame({
        canvas: canvasRef.current,
        onProgress: (current, total) => {
          if (total > 0) {
            setLoadingProgress((current / total) * 100);
          }
        }
      }).then(() => {
        setGameLoaded(true);
        // Ensure proper sizing after game loads
        setTimeout(updateCanvasSize, 100);
      }).catch(err => {
        setGameError('Failed to start game: ' + err.message);
      });
    };

    // Since the script is loaded via layout, we can directly initialize
    if (window.Engine) {
      initializeGame();
    } else {
      // Fallback: wait for script to load
      const checkEngine = setInterval(() => {
        if (window.Engine) {
          clearInterval(checkEngine);
          initializeGame();
        }
      }, 100);

      // Cleanup interval after 10 seconds
      setTimeout(() => {
        clearInterval(checkEngine);
        if (!window.Engine) {
          setGameError('Failed to load game engine.');
        }
      }, 10000);
    }

    return () => {
      if (engineRef.current?.requestQuit) engineRef.current.requestQuit();
      window.removeEventListener('resize', updateCanvasSize);
    };
  }, [gameData]);

  if (!isClient) {
    return null;
  }

  return (
    <div className="min-h-screen bg-black flex flex-col overflow-hidden">
      {/* Header */}
      <div className="bg-gray-900 p-4 flex items-center justify-between flex-shrink-0">
        <div className="flex items-center space-x-4">
          <Link
            href={currentChatId ? `/chat/${currentChatId}` : "/"}
            className="flex items-center text-white hover:text-gray-300 transition-colors"
          >
            <ArrowLeft className="w-5 h-5 mr-2" />
            {currentChatId ? 'Back to Chat' : 'Back to Home'}
          </Link>
          
          {currentChatId && (
            <span className="text-gray-400 text-sm">
              Chat: {currentChatId}
            </span>
          )}
        </div>

        <h1 className="text-white text-xl font-bold">Alternate Timeline Story</h1>

        <div className="flex items-center space-x-2">
          {/* Save Status */}
          {isSavingStoryData && (
            <div className="flex items-center text-blue-400 text-sm">
              <Save className="w-4 h-4 mr-1 animate-pulse" />
              Saving...
            </div>
          )}
          
          {storyDataId && !isSavingStoryData && (
            <div className="flex items-center text-green-400 text-sm">
              <Save className="w-4 h-4 mr-1" />
              Saved
            </div>
          )}

          {/* Regenerate Button */}
          {!isGenerating && gameData && (
            <button
              onClick={regenerateScenario}
              className="flex items-center text-white hover:text-gray-300 transition-colors bg-gray-700 hover:bg-gray-600 px-3 py-1 rounded text-sm"
            >
              <RefreshCw className="w-4 h-4 mr-1" />
              Regenerate
            </button>
          )}
        </div>
      </div>

      {/* Loading State - Full Screen */}
      {(isGenerating || isLoadingFromAPI) && (
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center text-white max-w-md">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
            <p className="text-lg mb-2">
              {isLoadingFromAPI 
                ? 'Loading your story analysis...' 
                : hasExistingStoryData 
                ? 'Loading existing story...'
                : 'Generating your interactive story...'}
            </p>
            <p className="text-sm text-gray-400">
              {isLoadingFromAPI 
                ? 'Fetching data from your analysis session'
                : hasExistingStoryData
                ? 'Found existing story data in your account'
                : 'Creating personalized alternate timeline scenarios'}
            </p>
            
            {currentChatId && (
              <p className="text-xs text-gray-500 mt-2">
                Session: {currentChatId}
              </p>
            )}
          </div>
        </div>
      )}

      {/* Error State - Full Screen */}
      {error && (
        <div className="flex-1 flex items-center justify-center">
          <div className="bg-red-900/50 border border-red-700 p-6 rounded-lg text-center max-w-md">
            <h2 className="text-xl font-bold mb-2 text-white">Error</h2>
            <p className="mb-4 text-red-200">{error}</p>
            <div className="space-y-2">
              {currentChatId ? (
                <Link
                  href={`/chat/${currentChatId}`}
                  className="block px-6 py-3 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
                >
                  Return to Chat
                </Link>
              ) : (
                <Link
                  href="/"
                  className="block px-6 py-3 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
                >
                  Return to Analysis
                </Link>
              )}
              
              {bookInfo.bookTitle && userPrompt && (
                <button
                  onClick={regenerateScenario}
                  className="block w-full px-6 py-3 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
                >
                  Try Again
                </button>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Story Info Panel - Show when we have data */}
      {(bookInfo.bookTitle || userPrompt) && !error && !isGenerating && !isLoadingFromAPI && (
        <div className="bg-gray-800 border-b border-gray-700 p-4 text-sm">
          <div className="flex flex-wrap items-center justify-between max-w-6xl mx-auto">
            <div className="flex flex-wrap items-center space-x-4 text-gray-300">
              {bookInfo.bookTitle && (
                <span><strong>Story:</strong> {bookInfo.bookTitle}</span>
              )}
              {bookInfo.author && bookInfo.author !== 'Unknown Author' && (
                <span><strong>Author:</strong> {bookInfo.author}</span>
              )}
              {bookInfo.changeLocation && (
                <span><strong>Section:</strong> {bookInfo.changeLocation}</span>
              )}
            </div>
            {userPrompt && (
              <div className="text-gray-400 max-w-md truncate">
                <strong>Prompt:</strong> {userPrompt}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Game Container */}
      {!isGenerating && !isLoadingFromAPI && !error && gameData && (
        <div className="flex-1 flex flex-col overflow-hidden min-h-0">
          <div ref={containerRef} className="flex-1 relative flex items-center justify-center bg-gray-900 min-h-0 w-full">
            {/* Loading Screen */}
            {!gameLoaded && !gameError && (
              <div className="absolute inset-0 flex flex-col items-center justify-center z-10">
                <div className="text-white text-xl mb-4">Loading Interactive Story...</div>
                <div className="w-64 h-2 bg-gray-600 rounded-full overflow-hidden">
                  <div
                    className="h-full bg-blue-500 transition-all duration-300"
                    style={{ width: `${loadingProgress}%` }}
                  />
                </div>
                <div className="text-gray-300 mt-2">{Math.round(loadingProgress)}%</div>
                
                {hasExistingStoryData && (
                  <div className="text-green-400 text-sm mt-2">
                    ✓ Loaded from your saved progress
                  </div>
                )}
              </div>
            )}

            {/* Error Screen */}
            {gameError && (
              <div className="absolute inset-0 flex flex-col items-center justify-center z-10">
                <div className="text-white text-xl mb-4">Error Loading Game</div>
                <div className="text-red-200 text-center max-w-md mb-4">
                  {gameError}
                </div>
                <div className="space-x-4">
                  <button
                    onClick={() => window.location.reload()}
                    className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
                  >
                    Retry
                  </button>
                  {currentChatId && (
                    <Link
                      href={`/chat/${currentChatId}`}
                      className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors"
                    >
                      Back to Chat
                    </Link>
                  )}
                </div>
              </div>
            )}

            {/* Game Canvas */}
            <canvas
              ref={canvasRef}
              id="canvas"
              className="block"
              style={{
                imageRendering: 'pixelated',
                imageRendering: '-moz-crisp-edges',
                imageRendering: 'crisp-edges',
              }}
            >
              HTML5 canvas appears to be unsupported in the current browser.
              Please try updating or use a different browser.
            </canvas>
          </div>
        </div>
      )}
    </div>
  );
}
