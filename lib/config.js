const ENV = process.env.NEXT_PUBLIC_APP_ENV || 'development';

// Database table configurations
const DB_TABLES = {
  WAITLIST: 'waitlist',
  USERS: 'users'
};

const ENV_CONFIG = {
    development: {
        prefix: ''
    },
    production: {
        prefix: ''
    }
};

export const getTableName = (tableName) => {
const config = ENV_CONFIG[ENV] || ENV_CONFIG.development;
return `${config.prefix}${tableName}`;
};

export const getTables = () => {
return Object.entries(DB_TABLES).reduce((acc, [key, value]) => {
    acc[key] = getTableName(value);
    return acc;
}, {});
};

export const TABLES = getTables();

export const isDevelopment = () => ENV !== 'production';

export const currentEnv = ENV;

