export const authConfig = {
    apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
    cookieName: '__session',
    cookieSignatureKeys: [
        process.env.COOKIE_SIGNATURE_KEY,
        process.env.COOKIE_SIGNATURE_KEY_PREVIOUS,
    ],
    cookieSerializeOptions: {
        path: '/',
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
    },
    serviceAccount: {
        projectId: process.env.FIREBASE_PROJECT_ID,
        clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
        privateKey: process.env.FIREBASE_PRIVATE_KEY.replace(/\\n/g, '\n'),
    },
};
