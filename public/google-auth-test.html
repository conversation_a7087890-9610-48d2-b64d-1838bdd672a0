<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Google Auth Test</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      line-height: 1.6;
    }
    button {
      background-color: #4285F4;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
      margin: 10px 0;
    }
    button:hover {
      background-color: #3367D6;
    }
    pre {
      background-color: #f5f5f5;
      padding: 15px;
      border-radius: 4px;
      overflow: auto;
      max-height: 300px;
    }
    .log-container {
      margin-top: 20px;
    }
    .log-entry {
      margin-bottom: 5px;
      border-bottom: 1px solid #eee;
      padding-bottom: 5px;
    }
    .error {
      color: #D32F2F;
    }
    .success {
      color: #388E3C;
    }
    .info {
      color: #1976D2;
    }
    .session-info {
      background-color: #f0f8ff;
      padding: 10px;
      border-radius: 4px;
      margin-bottom: 15px;
    }
    .download-link {
      display: inline-block;
      margin-top: 10px;
      color: #1976D2;
      text-decoration: underline;
      cursor: pointer;
    }
  </style>
</head>
<body>
  <h1>Google Auth Test</h1>
  <p>This page tests the Google authentication flow and logs all steps and results.</p>
  
  <div class="session-info" id="sessionInfo">
    <strong>Test Session ID:</strong> <span id="sessionId"></span><br>
    <strong>Started:</strong> <span id="sessionStart"></span>
  </div>
  
  <div>
    <button id="testRedirect">Test Sign In with Redirect</button>
    <button id="testPopup">Test Sign In with Popup</button>
    <button id="checkAuthState">Check Auth State</button>
    <button id="testApiCall">Test API Call</button>
    <button id="signOut">Sign Out</button>
    <button id="clearLogs">Clear Logs</button>
    <button id="downloadLogs">Download Logs</button>
  </div>
  
  <div class="log-container">
    <h2>Logs</h2>
    <pre id="logs"></pre>
  </div>

  <!-- Load Firebase SDK -->
  <script type="module">
    // Import Firebase modules
    import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.8.0/firebase-app.js';
    import { 
      getAuth, 
      GoogleAuthProvider, 
      signInWithPopup, 
      signInWithRedirect, 
      getRedirectResult, 
      onAuthStateChanged,
      signOut
    } from 'https://www.gstatic.com/firebasejs/10.8.0/firebase-auth.js';

    // Get logs element
    const logsElement = document.getElementById('logs');
    const sessionIdElement = document.getElementById('sessionId');
    const sessionStartElement = document.getElementById('sessionStart');
    
    // Generate or retrieve session ID
    let sessionId = localStorage.getItem('googleAuthTestSessionId');
    if (!sessionId) {
      sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substring(2, 9);
      localStorage.setItem('googleAuthTestSessionId', sessionId);
      localStorage.setItem('googleAuthTestSessionStart', new Date().toISOString());
    }
    
    // Display session info
    sessionIdElement.textContent = sessionId;
    sessionStartElement.textContent = localStorage.getItem('googleAuthTestSessionStart') || 'Unknown';
    
    // Retrieve logs from localStorage
    let storedLogs = [];
    try {
      const savedLogs = localStorage.getItem('googleAuthTestLogs');
      if (savedLogs) {
        storedLogs = JSON.parse(savedLogs);
        
        // Display stored logs
        storedLogs.forEach(entry => {
          const logEntry = document.createElement('div');
          logEntry.className = `log-entry ${entry.type}`;
          logEntry.textContent = `[${entry.timestamp}] ${entry.message}`;
          logsElement.appendChild(logEntry);
        });
      }
    } catch (e) {
      console.error('Error loading stored logs:', e);
    }
    
    // Logger function
    function log(message, type = 'info') {
      const timestamp = new Date().toISOString();
      const formattedMessage = typeof message === 'object' 
        ? JSON.stringify(message, null, 2) 
        : message;
      
      // Create log entry for display
      const logEntry = document.createElement('div');
      logEntry.className = `log-entry ${type}`;
      logEntry.textContent = `[${timestamp}] ${formattedMessage}`;
      
      // Add to DOM
      logsElement.prepend(logEntry);
      
      // Log to console
      console[type === 'error' ? 'error' : 'log'](message);
      
      // Store in memory and localStorage
      storedLogs.push({
        timestamp,
        message: formattedMessage,
        type,
        sessionId
      });
      
      // Keep only the last 100 logs to avoid localStorage size limits
      if (storedLogs.length > 100) {
        storedLogs = storedLogs.slice(-100);
      }
      
      // Save to localStorage
      localStorage.setItem('googleAuthTestLogs', JSON.stringify(storedLogs));
    }

    // Initialize Firebase
    const firebaseConfig = {
      apiKey: "AIzaSyAxYGR4YbpcThPO9IcCNRPX15TlIzu4nGw",
      authDomain: "the-money-tales.firebaseapp.com",
      projectId: "the-money-tales",
      storageBucket: "the-money-tales.appspot.com",
      messagingSenderId: "34776109288",
      appId: "1:34776109288:web:bc9dd0feae7ac9ae5e395d"
    };

    log('Initializing Firebase...');
    const app = initializeApp(firebaseConfig);
    const auth = getAuth(app);
    
    // Set up auth state listener
    onAuthStateChanged(auth, (user) => {
      if (user) {
        log('Auth state changed: User is signed in', 'success');
        log({
          uid: user.uid,
          email: user.email,
          displayName: user.displayName,
          emailVerified: user.emailVerified,
          isAnonymous: user.isAnonymous,
          providerData: user.providerData
        });
      } else {
        log('Auth state changed: User is signed out', 'info');
      }
    });

    // Check for redirect result on page load
    window.addEventListener('load', async () => {
      log('Page loaded, checking for redirect result...');
      try {
        const result = await getRedirectResult(auth);
        if (result) {
          log('Redirect result found!', 'success');
          log({
            user: {
              uid: result.user.uid,
              email: result.user.email,
              displayName: result.user.displayName
            },
            operationType: result.operationType,
            providerId: result._tokenResponse?.providerId
          });
          
          // Get the Google OAuth access token
          const credential = GoogleAuthProvider.credentialFromResult(result);
          log('Credential obtained:', 'success');
          log({
            accessToken: credential.accessToken ? `${credential.accessToken.substring(0, 10)}...` : null,
            providerId: credential.providerId,
            signInMethod: credential.signInMethod
          });
          
          // Get the ID token
          const idToken = await result.user.getIdToken();
          log(`ID token obtained, length: ${idToken.length}`, 'success');
          
          // Store token in sessionStorage for API test
          sessionStorage.setItem('authToken', idToken);
          
          // Test API call automatically after redirect
          await testApiCallFunction();
        } else {
          log('No redirect result found');
        }
      } catch (error) {
        log('Error checking redirect result:', 'error');
        log(error.message, 'error');
        log(error.stack, 'error');
      }
    });

    // Test sign in with redirect
    document.getElementById('testRedirect').addEventListener('click', async () => {
      log('Starting Google sign in with redirect...');
      try {
        const provider = new GoogleAuthProvider();
        provider.addScope('email');
        provider.addScope('profile');
        
        // Add state parameter for tracking
        provider.setCustomParameters({
          prompt: 'select_account',
          state: `test_redirect_${sessionId}`
        });
        
        log('Provider configured, initiating redirect...');
        log('NOTE: You will be redirected away. Logs will be preserved.', 'info');
        
        // Small delay to ensure logs are saved
        await new Promise(resolve => setTimeout(resolve, 500));
        
        await signInWithRedirect(auth, provider);
        log('Redirect initiated, page will reload...'); // This won't be seen
      } catch (error) {
        log('Error starting redirect flow:', 'error');
        log(error.message, 'error');
      }
    });

    // Test sign in with popup
    document.getElementById('testPopup').addEventListener('click', async () => {
      log('Starting Google sign in with popup...');
      try {
        const provider = new GoogleAuthProvider();
        provider.addScope('email');
        provider.addScope('profile');
        
        log('Provider configured, opening popup...');
        const result = await signInWithPopup(auth, provider);
        
        log('Popup auth successful!', 'success');
        log({
          user: {
            uid: result.user.uid,
            email: result.user.email,
            displayName: result.user.displayName
          },
          operationType: result.operationType
        });
        
        // Get the Google OAuth access token
        const credential = GoogleAuthProvider.credentialFromResult(result);
        log('Credential obtained:', 'success');
        log({
          accessToken: credential.accessToken ? `${credential.accessToken.substring(0, 10)}...` : null,
          providerId: credential.providerId,
          signInMethod: credential.signInMethod
        });
        
        // Get the ID token
        const idToken = await result.user.getIdToken();
        log(`ID token obtained, length: ${idToken.length}`, 'success');
        
        // Store token in sessionStorage for API test
        sessionStorage.setItem('authToken', idToken);
      } catch (error) {
        log('Google popup auth error:', 'error');
        log(error.code, 'error');
        log(error.message, 'error');
        if (error.email) {
          log(`Email associated with error: ${error.email}`, 'error');
        }
        if (error.credential) {
          log(`Credential associated with error: ${error.credential}`, 'error');
        }
      }
    });

    // Check auth state
    document.getElementById('checkAuthState').addEventListener('click', () => {
      const user = auth.currentUser;
      if (user) {
        log('Current user:', 'success');
        log({
          uid: user.uid,
          email: user.email,
          displayName: user.displayName,
          emailVerified: user.emailVerified,
          isAnonymous: user.isAnonymous,
          providerData: user.providerData,
          metadata: {
            creationTime: user.metadata.creationTime,
            lastSignInTime: user.metadata.lastSignInTime
          }
        });
      } else {
        log('No user currently signed in', 'info');
      }
    });

    // Test API call function
    async function testApiCallFunction() {
      log('Testing API call...');
      const idToken = sessionStorage.getItem('authToken');
      
      if (!idToken) {
        log('No ID token found. Please sign in first.', 'error');
        return;
      }
      
      try {
        log(`Making API call with token (first 10 chars): ${idToken.substring(0, 10)}...`);
        const response = await fetch('/api/auth/login', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${idToken}`,
            'Content-Type': 'application/json'
          }
        });
        
        log(`API Response status: ${response.status} ${response.statusText}`);
        
        // Log response headers
        const headers = {};
        response.headers.forEach((value, key) => {
          headers[key] = value;
        });
        log('Response headers:', 'info');
        log(headers);
        
        // Log response body
        const responseText = await response.text();
        log('Response body:', 'info');
        log(responseText || '(empty response)');
        
        if (response.ok) {
          log('API call successful!', 'success');
        } else {
          log('API call failed', 'error');
        }
      } catch (error) {
        log('API call error:', 'error');
        log(error.message, 'error');
      }
    }

    // Test API call button
    document.getElementById('testApiCall').addEventListener('click', testApiCallFunction);

    // Sign out
    document.getElementById('signOut').addEventListener('click', async () => {
      try {
        log('Signing out...');
        await signOut(auth);
        sessionStorage.removeItem('authToken');
        log('Successfully signed out', 'success');
      } catch (error) {
        log('Error signing out:', 'error');
        log(error.message, 'error');
      }
    });

    // Clear logs
    document.getElementById('clearLogs').addEventListener('click', () => {
      logsElement.innerHTML = '';
      storedLogs = [];
      localStorage.setItem('googleAuthTestLogs', JSON.stringify(storedLogs));
      log('Logs cleared', 'info');
    });
    
    // Download logs
    document.getElementById('downloadLogs').addEventListener('click', () => {
      const logText = storedLogs.map(entry => 
        `[${entry.timestamp}][${entry.type}] ${entry.message}`
      ).join('\n');
      
      const blob = new Blob([logText], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `google-auth-test-logs-${sessionId}.txt`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      log('Logs downloaded', 'info');
    });
  </script>
</body>
</html>
